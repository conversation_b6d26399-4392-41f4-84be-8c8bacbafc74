<template>
    <el-main>
      <div class="title">业绩中心</div>
      <div class="c-title">欢迎回来，{{ userName }}！这是您本月的业绩概览。</div>

      <!-- 视图切换和筛选区域 -->
      <el-row :gutter="24" style="margin-top: 32px;">
        <el-col :span="8">
          <el-radio-group v-model="currentView" @change="handleViewChange">
            <el-radio-button label="monthly">按月汇总</el-radio-button>
            <el-radio-button label="content">按内容汇总</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="16" style="text-align:right;">
          <!-- 按月汇总的筛选器 -->
          <div v-if="currentView === 'monthly'">
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @change="handleDateRangeChange"
              :popper-class="'dark-date-picker'"
            />
          </div>
          <!-- 按内容汇总时不显示额外筛选器 -->
        </el-col>
      </el-row>

      <!-- 按内容汇总的额外筛选行 -->
      <div v-if="currentView === 'content'" class="form">
        <el-select
          v-model="selectedTasks"
          placeholder="筛选任务名称"
          multiple
          filterable
          clearable
          @change="handleFilterChange"
          style="width: 240px;"
        >
          <el-option
            v-for="task in taskNameOptions"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>

        <el-select
          v-model="selectedChannels"
          placeholder="筛选渠道码"
          multiple
          filterable
          clearable
          @change="handleFilterChange"
          style="width: 240px;"
        >
          <el-option
            v-for="channel in channelCodeOptions"
            :key="channel.value"
            :label="channel.label"
            :value="channel.value"
          />
        </el-select>
        <el-button @click="resetFilters">重置筛选</el-button>
      </div>

      <!-- 按月汇总视图 -->
      <div v-if="currentView === 'monthly'">
        <el-card class="kol-table-card">
          <div class="kol-table-title">月度业绩汇总</div>
          <el-table :data="monthlyData" v-loading="loading" border>
            <el-table-column prop="month" label="月份" width="120" />
            <el-table-column prop="total_income" label="入金金额" min-width="120">
              <template #default="scope">
                ${{ formatMoney(scope.row.total_income) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_clicks" label="总点击量" min-width="100">
              <template #default="scope">
                {{ formatNumber(scope.row.total_clicks) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_registers" label="总注册量" min-width="100">
              <template #default="scope">
                {{ formatNumber(scope.row.total_registers) }}
              </template>
            </el-table-column>
            <el-table-column prop="avg_conversion_rate" label="平均转化率" min-width="120">
              <template #default="scope">
                {{ (scope.row.avg_conversion_rate || 0).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column prop="total_ftt" label="FTT总值" min-width="120">
              <template #default="scope">
                {{ formatNumber(scope.row.total_ftt) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 按内容汇总视图 -->
      <div v-if="currentView === 'content'">
        <el-card class="kol-table-card">
          <div class="kol-table-title">内容效果汇总</div>
          <el-table :data="contentData" v-loading="loading" border>
            <el-table-column prop="campaign_name" label="任务名称" min-width="180" />
            <el-table-column prop="channel_code" label="渠道码" min-width="120" />
            <el-table-column prop="total_clicks" label="点击量" min-width="100">
              <template #default="scope">
                {{ formatNumber(scope.row.total_clicks) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_registers" label="注册量" min-width="100">
              <template #default="scope">
                {{ formatNumber(scope.row.total_registers) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_income" label="入金金额" min-width="120">
              <template #default="scope">
                ${{ formatMoney(scope.row.total_income) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_ftt" label="FTT值" min-width="120">
              <template #default="scope">
                {{ formatNumber(scope.row.total_ftt) }}
              </template>
            </el-table-column>
            <el-table-column prop="avg_conversion_rate" label="转化率" min-width="120">
              <template #default="scope">
                {{ (scope.row.avg_conversion_rate || 0).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column prop="active_days" label="活跃天数" min-width="100" />
          </el-table>
        </el-card>
      </div>
    </el-main>
  </template>

  <script setup>
  import { ref, onMounted, computed } from 'vue'
  import apiService from '@/utils/api'

  const monthlyData = ref([])
  const contentData = ref([])
  const currentView = ref('monthly')
  const selectedMonth = ref(new Date().toISOString().slice(0, 7)) // YYYY-MM格式
  const dateRange = ref([]) // 按月汇总的日期范围
  const taskNameOptions = ref([]) // 任务名称选项列表
  const channelCodeOptions = ref([]) // 渠道码选项列表
  const selectedTasks = ref([]) // 选中的任务名称
  const selectedChannels = ref([]) // 选中的渠道码
  const loading = ref(false) // 加载状态
  const userName = ref('Crypto先知') // 可根据登录信息动态获取

  // 获取当前用户信息
  const userInfo = computed(() => {
    const stored = localStorage.getItem('userInfo')
    return stored ? JSON.parse(stored) : { id: 1 }
  })

  function formatMoney(val) {
    if (val == null) return '0.00'
    return Number(val).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  function formatNumber(val) {
    if (val == null) return '0'
    return Number(val).toLocaleString()
  }

  // 获取筛选选项数据
  async function fetchFilterOptions() {
    try {
      const res = await apiService.getKolPerformanceFilterOptions({ kol_id: userInfo.value.id })
      taskNameOptions.value = res.data.task_names || []
      channelCodeOptions.value = res.data.channel_codes || []
    } catch (error) {
      console.error('获取筛选选项失败:', error)
    }
  }

  // 获取月度汇总数据
  async function fetchMonthlyData() {
    try {
      loading.value = true
      const params = { kol_id: userInfo.value.id }

      // 添加日期范围参数
      if (dateRange.value && dateRange.value.length === 2) {
        params.start_month = dateRange.value[0]
        params.end_month = dateRange.value[1]
      }

      const res = await apiService.getKolPerformanceMonthly(params)
      // const res = {
      //   data: [
      //     {}
      //   ]
      // };
      monthlyData.value = res.data
    } catch (error) {
      console.error('获取月度数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取内容汇总数据
  async function fetchContentData() {
    try {
      loading.value = true
      const params = {
        kol_id: userInfo.value.id
        // 不再传递月份参数，显示所有历史数据
      }

      // 添加筛选参数
      if (selectedTasks.value.length > 0) {
        params.task_names = selectedTasks.value.join(',')
      }
      if (selectedChannels.value.length > 0) {
        params.channel_codes = selectedChannels.value.join(',')
      }

      const res = await apiService.getKolPerformanceContent(params)
      contentData.value = res.data
    } catch (error) {
      console.error('获取内容数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 视图切换处理
  function handleViewChange(view) {
    currentView.value = view
    if (view === 'monthly') {
      fetchMonthlyData()
    } else if (view === 'content') {
      fetchFilterOptions() // 获取筛选选项
      fetchContentData()
    }
  }

  // 日期范围变化处理
  function handleDateRangeChange() {
    if (currentView.value === 'monthly') {
      fetchMonthlyData()
    }
  }

  // 月份切换处理函数已移除（按内容汇总不再需要月份筛选）

  // 筛选条件变化处理
  function handleFilterChange() {
    console.log('🔍 筛选条件变化:', {
      selectedTasks: selectedTasks.value,
      selectedChannels: selectedChannels.value
    })
    if (currentView.value === 'content') {
      fetchContentData()
    }
  }

  // 重置筛选条件
  function resetFilters() {
    selectedTasks.value = []
    selectedChannels.value = []
    fetchContentData()
  }

  onMounted(() => {
    fetchMonthlyData()
  })
  </script>

  <style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }

  .c-title {
    color: #999;
    font-size: 14px;
    padding: 8px 0;
  }
  .form {
    display: flex;
    margin-top: 12px;
    .el-select {
      margin-right: 12px;
    }
  }
  .stat-card {
    border: 2px solid #555;
    min-height: 110px;
    text-align: center;
    box-shadow: 0 4px 24px #0006;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 16px;
  }
  .stat-title {
    font-size: 15px;
    margin-bottom: 8px;
  }
  .stat-value {
    font-size: 28px;
    font-weight: bold;
  }
  .kol-table-card {
    margin-top: 32px;
  }
  .kol-table-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  </style>
