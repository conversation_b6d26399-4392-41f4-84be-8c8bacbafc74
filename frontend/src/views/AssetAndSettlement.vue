<template>
  <el-main>
    <div class="title">资产与结算</div>
    <div class="c-title">管理您的收入和收款信息</div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="cards">
      <el-col :span="6">
        <el-card class="stat-card card1">
          <div class="stat-title">总结算金额</div>
          <div class="stat-value">¥{{ formatMoney(summary.total_amount) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card  card2">
          <div class="stat-title">待支付金额</div>
          <div class="stat-value">¥{{ formatMoney(summary.pending_amount) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card card3">
          <div class="stat-title">已支付金额</div>
          <div class="stat-value">¥{{ formatMoney(summary.paid_amount) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card  card4">
          <div class="stat-title">结算次数</div>
          <div class="stat-value">{{ summary.total_count }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选器 -->
    <div class="module-title">历史结算记录</div>
    <div class="filters">
      <label>选择结算月份</label>
      <el-select v-model="filters.settlement_month" clearable @change="handleFilterChange">
        <el-option label="2024-01" value="2024-01" />
        <el-option label="2023-12" value="2023-12" />
        <el-option label="2023-11" value="2023-11" />
        <el-option label="2023-10" value="2023-10" />
      </el-select>
      <label>选择状态</label>
      <el-select v-model="filters.status" clearable @change="handleFilterChange">
        <el-option label="已支付" value="paid" />
        <el-option label="待支付" value="pending" />
        <el-option label="失败" value="failed" />
      </el-select>
      <label>选择支付方式</label>
      <el-select v-model="filters.payment_method" clearable @change="handleFilterChange">
        <el-option label="加密钱包" value="crypto_wallet" />
        <el-option label="银行转账" value="bank_transfer" />
      </el-select>
      <el-button @click="resetFilters">重置筛选</el-button>
    </div>

    <!-- 历史结算记录 -->
    <el-table :data="history" border v-loading="loading" class="records">
      <el-table-column prop="task_name" label="任务名称" min-width="180" />
      <el-table-column prop="settlement_date" label="结算日期" width="120">
        <template #default="scope">
          {{ scope.row.settlement_date ? formatDate(scope.row.settlement_date, 'YYYY-MM-DD') : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="total_fee" label="总金额" width="120">
        <template #default="scope">¥{{ formatMoney(scope.row.total_fee) }}</template>
      </el-table-column>
      <el-table-column prop="base_fee" label="基础费用" width="120">
        <template #default="scope">¥{{ formatMoney(scope.row.base_fee) }}</template>
      </el-table-column>
      <el-table-column prop="performance_fee" label="效果佣金" width="120">
        <template #default="scope">¥{{ formatMoney(scope.row.performance_fee) }}</template>
      </el-table-column>
      <el-table-column prop="payment_method" label="支付方式" width="100">
        <template #default="scope">
          {{ scope.row.payment_method === 'crypto_wallet' ? '加密钱包' : '银行转账' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.status === 'paid' ? 'success' : (scope.row.status === 'failed' ? 'danger' : 'warning')"
            effect="dark">
            {{ scope.row.status === 'paid' ? '已支付' : scope.row.status === 'pending' ? '待支付' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="paid_time" label="支付时间" width="160">
        <template #default="scope">
          {{ scope.row.paid_time ? formatDate(scope.row.paid_time) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="tx_hash" label="交易哈希" width="140">
        <template #default="scope">
          <a v-if="scope.row.tx_hash" :href="`https://etherscan.io/tx/${scope.row.tx_hash}`" target="_blank"
            style="color:#409EFF;">
            {{ formatTxHash(scope.row.tx_hash) }}
          </a>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-if="total > pageSize" v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
      :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" style="margin-top:20px; text-align:right;" :background="true" />

    <!-- 详情弹窗 -->
    <el-drawer v-model="detailDialogVisible" :title="`结算详情 - ${currentDetail?.task_name || ''}`" size="40%"
      :before-close="handleDetailClose">
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-section">
          <h4>📊 基础信息</h4>
          <div class="detail-item">
            <span class="label">任务名称：</span>
            <span class="value">{{ currentDetail.task_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结算月份：</span>
            <span class="value">{{ currentDetail.settlement_month }}</span>
          </div>
          <div class="detail-item">
            <span class="label">结算日期：</span>
            <span class="value">{{ currentDetail.settlement_date || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDate(currentDetail.created_at) }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>💰 费用明细</h4>
          <div class="detail-item">
            <span class="label">营销次数：</span>
            <span class="value">{{ currentDetail.marketing_count }} 次</span>
          </div>
          <div class="detail-item">
            <span class="label">营销单价：</span>
            <span class="value">¥{{ formatMoney(currentDetail.base_reward) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">基础费用：</span>
            <span class="value">¥{{ formatMoney(currentDetail.base_fee) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果值：</span>
            <span class="value">{{ currentDetail.performance_value }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果单价：</span>
            <span class="value">¥{{ formatMoney(currentDetail.performance_rate) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">效果佣金：</span>
            <span class="value">¥{{ formatMoney(currentDetail.performance_fee) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">费用总计：</span>
            <span class="value" style="font-weight: bold; color: #67c23a;">¥{{ formatMoney(currentDetail.total_fee)
              }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>💳 支付信息</h4>
          <div class="detail-item">
            <span class="label">支付方式：</span>
            <span class="value">{{ currentDetail.payment_method === 'crypto_wallet' ? '加密钱包' : '银行转账' }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.payment_network">
            <span class="label">支付网络：</span>
            <span class="value">{{ currentDetail.payment_network }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.wallet_address">
            <span class="label">钱包地址：</span>
            <span class="value" style="word-break: break-all;">{{ currentDetail.wallet_address }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.tx_hash">
            <span class="label">交易哈希：</span>
            <span class="value" style="word-break: break-all;">
              <a :href="`https://etherscan.io/tx/${currentDetail.tx_hash}`" target="_blank" style="color:#409EFF;">
                {{ currentDetail.tx_hash }}
              </a>
            </span>
          </div>
          <div class="detail-item" v-if="currentDetail.paid_time">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatDate(currentDetail.paid_time) }}</span>
          </div>
          <div class="detail-item" v-if="currentDetail.payment_note">
            <span class="label">支付备注：</span>
            <span class="value">{{ currentDetail.payment_note }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </el-main>
</template>

<script setup>
import { ref, onMounted } from 'vue'

import apiService from '@/utils/api'

const history = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

function formatMoney(val) {
  if (val == null) return '0.00'
  return Number(val).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

function formatDate(dateStr, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`
  } else if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  return date.toLocaleString('zh-CN')
}

function formatTxHash(hash) {
  if (!hash) return '-'
  return hash.length > 20 ? `${hash.slice(0, 10)}...${hash.slice(-10)}` : hash
}

async function fetchHistory() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('💰 User not logged in, skipping settlement history fetch')
    history.value = []
    total.value = 0
    loading.value = false
    return
  }

  loading.value = true
  try {
    const res = await apiService.getKolSettlements({
      params: {
        skip: (currentPage.value - 1) * pageSize.value,
        limit: pageSize.value,
        ...filters.value
      }
    })

    // const res = {
    //   data: {
    //     data: [
    //       { task_name: '任务名称' },
    //       { task_name: '任务名称' },
    //       { task_name: '任务名称' },
    //       { task_name: '任务名称' },
    //       { task_name: '任务名称' },
    //     ],
    //     total: 23
    //   }
    // }

    // 适配新的数据结构
    if (res.data && res.data.data) {
      history.value = res.data.data
      total.value = res.data.total || 0

      // 更新统计数据
      if (res.data.summary) {
        summary.value = res.data.summary
      }
    } else if (Array.isArray(res.data)) {
      // 兼容旧格式
      history.value = res.data
      total.value = res.data.length
    } else {
      history.value = []
      total.value = 0
    }
  } catch (e) {
    console.error('获取结算历史失败:', e)
    history.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

function handleSizeChange(newSize) {
  pageSize.value = newSize
  currentPage.value = 1
  fetchHistory()
}

function handleCurrentChange(newPage) {
  currentPage.value = newPage
  fetchHistory()
}

function handleFilterChange() {
  currentPage.value = 1
  fetchHistory()
}

function resetFilters() {
  filters.value = {
    settlement_month: null,
    status: null,
    payment_method: null
  }
  currentPage.value = 1
  fetchHistory()
}

function showDetail(row) {
  currentDetail.value = row
  detailDialogVisible.value = true
}

function handleDetailClose() {
  detailDialogVisible.value = false
  currentDetail.value = null
}

const filters = ref({
  settlement_month: null,
  status: null,
  payment_method: null
})

const summary = ref({
  total_amount: 0,
  pending_amount: 0,
  paid_amount: 0,
  total_count: 0
})

const currentDetail = ref(null)
const detailDialogVisible = ref(false)

onMounted(() => {
  fetchHistory()
})
</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
}
.c-title {
  color: #999;
  font-size: 14px;
  padding: 8px 0;
}
.module-title {
  margin-top: 32px;
  font-size: 20px;
}
.cards {
  margin-top: 32px;
}
.filters,
.records {
  margin-top: 16px;
}
.filters {
  display: flex;
  align-items: center;
  color: #999;
  label {
    margin-right: 8px;
  }
  .el-select {
    max-width: 180px;
    margin-right: 16px;
  }
}


.stat-card {
  text-align: center;
  padding: 10px 0;
  color: #fff;
  background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

  &.card2 {
    background: linear-gradient(to bottom right, #e74888, #bc53a1);
  }

  &.card3 {
    background: linear-gradient(to bottom right, #825dbf, #5345b4);
  }

  &.card4 {
    background: linear-gradient(to bottom right, #fbb728, #f68254);
  }
}

.stat-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
}

.detail-content {
}

.detail-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-section h4 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-item .label {
  font-size: 15px;
  color: #aaa;
  font-weight: bold;
}

.detail-item .value {
  font-size: 15px;
  font-weight: normal;
  word-break: break-all;
}

.detail-item .value a {
  color: #409EFF;
  text-decoration: none;
}

.detail-item .value a:hover {
  text-decoration: underline;
}
</style>
