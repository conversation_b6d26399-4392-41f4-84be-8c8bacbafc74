<template>
  <div class="kol-detail-container">
    <div class="title">
      <el-icon><ArrowLeft @click="goBack" /></el-icon>
      <span>KOL详细资料</span>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-container>
        <div>加载中...</div>
      </el-loading-container>
    </div>

    <!-- KOL详细信息 -->
    <div v-else-if="kolDetail" class="kol-detail-content">
      <!-- 头部信息 -->
      <div class="kol-header">
        <div class="kol-avatar-section">
          <div :style="avatarStyle" class="kol-avatar">
            {{ (kolDetail.platform_username || 'U').charAt(0).toUpperCase() }}
          </div>
          <div class="verified-badge" v-if="kolDetail.verified">
            <i class="el-icon-success" style="color: #1890ff;"></i>
            <span>已认证</span>
          </div>
        </div>

        <div class="kol-basic-info">
          <div class="kol-name">
            <h1>{{ kolDetail.platform_name || kolDetail.platform_username }}</h1>
            <span class="username">@{{ kolDetail.platform_username }}</span>
          </div>

          <div class="platform-info">
            <el-tag :type="getPlatformTagType(kolDetail.platform)">{{ kolDetail.platform || '未知平台' }}</el-tag>
            <span class="platform-id">ID: {{ kolDetail.platform_id }}</span>
          </div>

          <div class="description" v-if="kolDetail.description">
            <p>{{ kolDetail.description }}</p>
          </div>

          <div class="location" v-if="kolDetail.location">
            <i class="el-icon-location"></i>
            <span>{{ kolDetail.location }}</span>
          </div>

          <div class="tags-section" v-if="kolDetail.tag_name">
            <el-tag
              v-for="tag in getTagList(kolDetail.tag_name)"
              :key="tag"
              class="tag-item"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-card card1">
            <div class="stat-value">{{ formatNumber(kolDetail.followers_count || 0) }}</div>
            <div class="stat-label">粉丝数</div>
          </div>
          <div class="stat-card card2">
            <div class="stat-value">{{ formatNumber(kolDetail.following_count || 0) }}</div>
            <div class="stat-label">关注数</div>
          </div>
          <div class="stat-card card3">
            <div class="stat-value">{{ formatNumber(kolDetail.tweet_count || 0) }}</div>
            <div class="stat-label">发布数</div>
          </div>
          <div class="stat-card  card4">
            <div class="stat-value">{{ formatNumber(kolDetail.like_count || 0) }}</div>
            <div class="stat-label">获赞数</div>
          </div>
        </div>
      </div>

      <!-- 详细统计 -->
      <div class="detailed-stats">
        <el-card class="stat-detail-card">
          <template #header>
            <span class="m-title">互动数据</span>
          </template>
          <div class="metric-item">
            <span class="metric-label">互动率</span>
            <span class="metric-value">{{ kolDetail.engagement_rate || 0 }}%</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">平均点赞数/帖子</span>
            <span class="metric-value">{{ kolDetail.avg_likes_per_post || 0 }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最近发布数量</span>
            <span class="metric-value">{{ kolDetail.recent_posts_count || 0 }} 条</span>
          </div>
        </el-card>
        <el-card class="stat-detail-card">
          <template #header>
            <span class="m-title">合作信息</span>
          </template>
          <div class="metric-item">
            <span class="metric-label">合作状态</span>
            <el-tag :type="getCooperationTagType(kolDetail.cooperation_status)">
              {{ kolDetail.cooperation_status || '可邀请' }}
            </el-tag>
          </div>
          <div class="metric-item">
            <span class="metric-label">已完成任务</span>
            <span class="metric-value">{{ kolDetail.total_tasks_completed || 0 }} 个</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">评分</span>
            <el-rate
              v-model="kolDetail.rating_score"
              disabled
              show-score
              text-color="#ff9900"
            />
          </div>
        </el-card>
      </div>

      <!-- 账号信息 -->
      <div class="account-info">
        <el-card>
          <template #header>
            <span style="font-weight: bold;">账号信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="注册时间">
              {{ formatDate(kolDetail.account_created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="资料创建时间">
              {{ formatDate(kolDetail.create_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">
              {{ formatDate(kolDetail.update_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后同步">
              {{ formatDate(kolDetail.last_synced_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="认证类型" v-if="kolDetail.verified">
              <el-tag size="small">{{ getVerifiedTypeText(kolDetail.verified_type) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="资料链接" v-if="kolDetail.profile_url">
              <el-link :href="kolDetail.profile_url" target="_blank" type="primary">
                查看原始资料
              </el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-result icon="error" title="加载失败" sub-title="无法获取KOL详细信息">
        <template #extra>
          <el-button type="primary" @click="goBack">返回</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import apiService from '@/utils/api'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const kolDetail = ref(null)

// 获取KOL详细信息
async function fetchKOLDetail() {
  const kolId = route.params.id
  if (!kolId) {
    ElMessage.error('KOL ID参数缺失')
    goBack()
    return
  }

  try {
    loading.value = true
    const response = await apiService.getKolDetail(kolId)
    kolDetail.value = response.data
    console.log('KOL详细信息:', kolDetail.value)
  } catch (error) {
    console.error('获取KOL详细信息失败:', error)
    const errorMessage = error.response?.data?.detail || error.message || '获取KOL详细信息失败'
    ElMessage.error(errorMessage)
    kolDetail.value = null

    // 如果是404错误，3秒后自动返回上一页
    if (error.response?.status === 404) {
      setTimeout(() => {
        goBack()
      }, 3000)
    }
  } finally {
    loading.value = false
  }
}

// 格式化数字显示
function formatNumber(num) {
  if (!num) return '0'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取标签列表
function getTagList(tagString) {
  if (!tagString) return []
  return tagString.split(',').map(tag => tag.trim()).filter(tag => tag)
}

// 获取平台标签类型
function getPlatformTagType(platform) {
  const platformTypes = {
    'Twitter': 'primary',
    'YouTube': 'danger',
    'Medium': 'warning',
    'Instagram': 'success',
    'TikTok': 'info'
  }
  return platformTypes[platform] || 'default'
}

// 获取合作状态标签类型
function getCooperationTagType(status) {
  const statusTypes = {
    '合作中': 'warning',
    '可邀请': 'success',
    '繁忙': 'info',
    '暂停': 'danger'
  }
  return statusTypes[status] || 'default'
}

// 获取认证类型文本
function getVerifiedTypeText(type) {
  const verifiedTypes = {
    'blue': '蓝V认证',
    'business': '企业认证',
    'government': '政府认证'
  }
  return verifiedTypes[type] || '已认证'
}

// 头像样式
const avatarStyle = computed(() => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const colorIndex = kolDetail.value?.id % colors.length || 0
  return {
    backgroundColor: colors[colorIndex],
    color: '#fff'
  }
})

// 处理邀请操作
function handleInvite() {
  // 跳转到邀请页面或打开邀请弹窗
  ElMessage.info('邀请功能开发中...')
}

// 返回上一页
function goBack() {
  router.back()
}

onMounted(() => {
  fetchKOLDetail()
})
</script>

<style scoped>
.kol-detail-container {
}

.title {
  color: #444;
  font-size: 24px;
  display: flex;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.kol-detail-content {
  background: #fff;
  padding: 20px;
  margin-top: 32px;
}

.kol-header {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  align-items: flex-start;
}

.kol-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.kol-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #1890ff;
}

.kol-basic-info {
  flex: 1;
}

.kol-name h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.username {
  color: #888;
  font-size: 16px;
}

.platform-info {
  margin: 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.platform-id {
  color: #888;
  font-size: 14px;
}

.description {
  margin: 16px 0;
  line-height: 1.6;
  color: #666;
}

.location {
  margin: 12px 0;
  color: #888;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tags-section {
  margin: 16px 0;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-card {
  padding: 20px;
  text-align: center;
  color: #fff;
  background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

  &.card2 {
    background: linear-gradient(to bottom right, #e74888, #bc53a1);
  }

  &.card3 {
    background: linear-gradient(to bottom right, #825dbf, #5345b4);
  }

  &.card4 {
    background: linear-gradient(to bottom right, #fbb728, #f68254);
  }
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
}

.detailed-stats {
  margin-bottom: 32px;
  display: flex;
  .stat-detail-card {
    flex: 1;
    &:first-of-type {
      margin-right: 20px;
    }
    .m-title {
      font-weight: bold;
    }
  }
}


.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #efefef;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 500;
  color: #888;
}


.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

</style>
