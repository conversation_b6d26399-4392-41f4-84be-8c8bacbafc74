<template>
  <div class="kol-center">
    <div class="title">个人中心</div>

    <div class="center-content">
      <!-- 个人信息卡片 -->
      <div class="info-card">
        <div class="card-title">个人信息卡片</div>
        <div class="info-item">
          <span class="icon">👤</span>
          <span class="label">{{ userInfo.nickname || userInfo.username }}</span>
        </div>
        <div class="info-item">
          <span class="icon">📧</span>
          <span class="label">{{ maskEmail(userInfo.email) }}</span>
        </div>
        <div class="info-item">
          <span class="icon">📅</span>
          <span class="label">注册时间: {{ formatDate(userInfo.create_time) }}</span>
        </div>
        <div class="info-item">
          <span class="icon">🔔</span>
          <span class="label">KOL状态: <span class="status-active">✅ 已激活</span></span>
        </div>
        <div class="info-item">
          <span class="icon">💰</span>
          <span class="label">收款地址: {{ maskWallet(userInfo.wallet_address) }}</span>
        </div>
      </div>

      <!-- Twitter OAuth绑定组件 -->
      <TwitterOAuthBinding />

      <!-- 专业标签 -->
      <div class="info-card" style="max-width: 360px;">
        <div class="card-title">
          专业标签
          <el-button link @click="editTags" class="edit-btn">编辑</el-button>
        </div>
        <div class="tags-section">
          <el-tag v-for="tag in tags" :key="tag" type="success" style="margin-right: 8px; margin-bottom: 8px;">
            {{ tag }}
          </el-tag>
        </div>

        <div class="description-section">
          <div class="section-header">
            <span class="icon">📝</span>
            <span class="label">个人简介:</span>
            <el-button link @click="editDescription" class="edit-btn">编辑</el-button>
          </div>
          <div class="description-content">
            {{ kolProfile.description || '暂无个人简介' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑标签弹窗 -->
    <el-drawer v-model="showTagsDialog" title="编辑专业标签" width="500px">
      <div class="tags-edit">
        <div style="margin-bottom: 15px; color: #aaa;">最多选择3个标签</div>
        <div class="selected-tags">
          <el-tag v-for="tag in editingTags" :key="tag" closable @close="removeEditingTag(tag)"
            style="margin-right: 8px;">
            {{ tag }}
          </el-tag>
          <el-input v-if="inputVisible" ref="inputRef" v-model="inputValue" size="small" style="width: 120px;"
            @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
          <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
        </div>
        <div class="preset-tags">
          <el-tag v-for="tag in presetTags" :key="tag" :type="editingTags.includes(tag) ? 'success' : 'info'"
            @click="toggleEditingTag(tag)" style="margin-right: 8px; margin-bottom: 8px; cursor: pointer;">
            {{ tag }}
          </el-tag>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTagsDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTags" :loading="saving">保存</el-button>
      </template>
    </el-drawer>

    <!-- 编辑简介弹窗 -->
    <el-drawer v-model="showDescDialog" title="编辑个人简介" size="40%">
      <el-input v-model="editingDescription" type="textarea" :rows="6" placeholder="请简单介绍您的专业领域、经验和内容创作能力..."
        maxlength="500" show-word-limit />
      <template #footer>
        <el-button @click="showDescDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDescription" :loading="saving">保存</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useUserStore } from '@/store/user'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'
import TwitterOAuthBinding from '@/components/TwitterOAuthBinding.vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

const kolProfile = ref({})
const saving = ref(false)
const showTagsDialog = ref(false)
const showDescDialog = ref(false)
const editingTags = ref([])
const editingDescription = ref('')
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

const presetTags = ['DeFi', 'NFT', 'GameFi', 'Layer2', '交易', '投资', '技术分析', '项目评测', '教育科普', '新闻资讯']

const tags = computed(() => {
  if (!kolProfile.value.tag_name) return []
  return kolProfile.value.tag_name.split(', ').filter(tag => tag.trim())
})

onMounted(() => {
  fetchKolProfile()
})

async function fetchKolProfile() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('👤 User not logged in, skipping KOL profile fetch')
    kolProfile.value = {}
    return
  }

  try {
    // 🔧 修复：使用新的专门接口获取当前用户的KOL详情
    const response = await apiService.getCurrentUserKolDetail()
    kolProfile.value = response.data || {}
    // kolProfile.value = {
    //   tag_name: 'tag1,tag2',
    //   description: '这是一段普普通通的自我介绍哈哈哈，这是一段普普通通的自我介绍哈哈哈，这是一段普普通通的自我介绍哈哈哈，这是一段普普通通的自我介绍哈哈哈',
    // }
    console.log('✅ 当前用户KOL资料加载成功')
  } catch (error) {
    console.error('获取KOL资料失败:', error)
    if (error.response?.status === 404) {
      console.log('ℹ️ 当前用户还没有创建KOL资料')
      kolProfile.value = {}
    }
  }
}

// Twitter数据更新功能现在由TwitterOAuthBinding组件处理

// Twitter绑定功能现在由TwitterOAuthBinding组件处理

function editTags() {
  editingTags.value = [...tags.value]
  showTagsDialog.value = true
}

function editDescription() {
  editingDescription.value = kolProfile.value.description || ''
  showDescDialog.value = true
}

function removeEditingTag(tag) {
  const index = editingTags.value.indexOf(tag)
  if (index > -1) {
    editingTags.value.splice(index, 1)
  }
}

function toggleEditingTag(tag) {
  if (editingTags.value.includes(tag)) {
    removeEditingTag(tag)
  } else if (editingTags.value.length < 3) {
    editingTags.value.push(tag)
  } else {
    ElMessage.warning('最多只能选择3个标签')
  }
}

function showInput() {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function handleInputConfirm() {
  if (inputValue.value && editingTags.value.length < 3) {
    if (!editingTags.value.includes(inputValue.value)) {
      editingTags.value.push(inputValue.value)
    }
  } else if (editingTags.value.length >= 3) {
    ElMessage.warning('最多只能选择3个标签')
  }
  inputVisible.value = false
  inputValue.value = ''
}

async function saveTags() {
  saving.value = true
  try {
    const tagString = editingTags.value.join(', ')
    await apiService.updateKolProfile({
      tag_name: tagString
    })

    kolProfile.value.tag_name = tagString
    showTagsDialog.value = false
    ElMessage.success('标签保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

async function saveDescription() {
  saving.value = true
  try {
    await apiService.updateKolProfile({
      description: editingDescription.value
    })

    kolProfile.value.description = editingDescription.value
    showDescDialog.value = false
    ElMessage.success('简介保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

function maskEmail(email) {
  if (!email) return ''
  const [username, domain] = email.split('@')
  const maskedUsername = username.slice(0, 3) + '***'
  return `${maskedUsername}@${domain}`
}

function maskWallet(address) {
  if (!address) return '未设置'
  return `${address.slice(0, 8)}...${address.slice(-6)}`
}

function formatNumber(num) {
  return num ? num.toLocaleString() : '0'
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}
</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
  margin-bottom: 32px;
}

.center-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  padding-bottom: 32px;
}

.info-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 20px;
  min-width: 330px;
  box-shadow: #eee 2px 2px 2px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  line-height: 1.6;
}

.info-item .icon {
  margin-right: 10px;
  font-size: 16px;
}

.info-item .label {
}

.status-active {
  color: #4CAF50;
}

/* Twitter相关样式已移除，现在由TwitterOAuthBinding组件处理 */

.update-button {
  margin-top: 15px;
  text-align: center;
}

.tags-section {
  margin-bottom: 20px;
}

.description-section {
  border-top: 1px solid #eeee;
  padding-top: 15px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.section-header .icon {
  margin-right: 8px;
}

.section-header .label {
  flex: 1;
  font-weight: bold;
}

.description-content {
  color: #666;
  line-height: 1.6;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #eee;
  background: #f6f9f8;
}

.edit-btn {
  color: #409EFF !important;
  padding: 0 !important;
  font-size: 14px !important;
}

.tags-edit {
  color: #fff;
}

.selected-tags {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.preset-tags {
  border-top: 1px solid #eee;
  padding-top: 15px;
}
</style>
