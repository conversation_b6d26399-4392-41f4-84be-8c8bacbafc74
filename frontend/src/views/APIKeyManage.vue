<template>
  <el-main>
    <div class="title">API 密钥</div>
    <el-card class="main">
      <el-button type="primary" @click="createKey">创建新API Key</el-button>
      <el-table :data="apiKeys" border class="table">
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column prop="api_key" label="API Key" min-width="240">
          <template #default="scope">
            <span>
              {{ maskKey(scope.row.api_key) }}
              <el-button
                size="small"
                type="primary"
                style="margin-left:8px;"
                @click="copyKey(scope.row.api_key)"
              >复制</el-button>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                active-color="#13ce66"
                inactive-color="#dcdfe6"
                @change="val => toggleStatusSwitch(scope.row, val)"
              />
              <span :style="{
                background: scope.row.status === 1 ? '#d3f9d8' : '#f5f5f5',
                color: scope.row.status === 1 ? '#2e7d32' : '#888',
                borderRadius: '6px',
                padding: '2px 8px',
                fontSize: '12px',
                fontWeight: 500
              }">
                {{ scope.row.status === 1 ? '已启用' : '已禁用' }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" />
        <el-table-column prop="permissions" label="权限" min-width="100" />
        <el-table-column prop="created_at" label="创建时间" min-width="120">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template #default="scope">
            <el-button size="small" type="primary" @click="editKey(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="showDeleteDialog(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 编辑弹窗 -->
      <el-dialog v-model="editDialogVisible" title="编辑API Key" width="400px">
        <el-form :model="editForm" label-width="80px">
          <el-form-item label="名称">
            <el-input v-model="editForm.name" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="editForm.remark" />
          </el-form-item>
          <el-form-item label="权限">
            <el-input v-model="editForm.permissions" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
        </template>
      </el-dialog>
      <el-dialog v-model="deleteDialogVisible" title="删除确认" width="320px" :close-on-click-modal="false">
        <div>确定要删除该 API Key 吗？</div>
        <template #footer>
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">删除</el-button>
        </template>
      </el-dialog>
    </el-card>
  </el-main>
</template>

<script setup>
import { ref, onMounted } from 'vue'

import { ElMessage } from 'element-plus'
import apiService from '@/utils/api'

const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
const apiKeys = ref([])
const editDialogVisible = ref(false)
const editForm = ref({ id: null, name: '', remark: '', permissions: '', api_key: '' })
const deleteDialogVisible = ref(false)
const deleteRow = ref(null)

function maskKey(key) {
  if (!key) return ''
  return key.slice(0, 10) + '...' + key.slice(-10)
}

function copyKey(key) {
  navigator.clipboard.writeText(key)
  ElMessage.success('已复制')
}

function formatDate(val) {
  if (!val) return ''
  const d = typeof val === 'string' ? new Date(val) : val
  if (isNaN(d.getTime())) return val
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const h = String(d.getHours()).padStart(2, '0')
  const min = String(d.getMinutes()).padStart(2, '0')
  const s = String(d.getSeconds()).padStart(2, '0')
  return `${y}-${m}-${day} ${h}:${min}:${s}`
}

async function fetchKeys() {
  try {
    if (!userInfo?.id) {
      ElMessage.error('用户信息不完整，请重新登录')
      return
    }
    const res = await apiService.getApiKeys({ user_id: userInfo.id })
    apiKeys.value = Array.isArray(res.data) ? res.data : []
  } catch (error) {
    console.error('获取API Keys失败:', error)
    ElMessage.error(error.response?.data?.detail || '获取API Keys失败')
    apiKeys.value = []
  }
}

async function createKey() {
      await apiService.createApiKey({ user_id: userInfo.id, name: 'default', permissions: 'ALL' })
  ElMessage.success('创建成功')
  fetchKeys()
}

async function toggleStatusSwitch(row, val) {
  await apiService.updateApiKey({
    id: row.id,
    status: val,
    api_key: row.api_key
  })
  ElMessage.success(val === 1 ? '已启用' : '已禁用')
  fetchKeys()
}

function editKey(row) {
  editForm.value = { ...row }
  editDialogVisible.value = true
}

async function saveEdit() {
  await apiService.updateApiKey({
    id: editForm.value.id,
    name: editForm.value.name,
    remark: editForm.value.remark,
    permissions: editForm.value.permissions,
    api_key: editForm.value.api_key
  })
  ElMessage.success('保存成功')
  editDialogVisible.value = false
  fetchKeys()
}

function showDeleteDialog(row) {
  deleteRow.value = row
  deleteDialogVisible.value = true
}

async function confirmDelete() {
  if (!deleteRow.value) return
  await apiService.updateApiKey({
    id: deleteRow.value.id,
    status: 2,
    api_key: deleteRow.value.api_key
  })
  ElMessage.success('已删除')
  deleteDialogVisible.value = false
  fetchKeys()
}

onMounted(fetchKeys)
</script>

<style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }
  .main {
    margin-top: 32px;
  }
  .table {
    margin-top: 20px;
  }
</style>
