<template>
  <el-main>
    <div class="title">数据归因看板</div>
    <div class="c-title">实时监控所有营销活动的核心数据</div>

    <!-- 顶部统计卡片 -->
    <el-row :gutter="24" class="count">
      <el-col :span="6">
        <el-card class="stat-card card1">
          <div class="stat-title">活跃营销活动</div>
          <div class="stat-value">
            <img src="../assets/icon/event.png" />
            <span>{{ summary.task_count }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card card2">
          <div class="stat-title">总点击量</div>
          <div class="stat-value">
            <img src="../assets/icon/click.png" />
            <span>{{ formatNumber(summary.total.click) }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card card3">
          <div class="stat-title">总注册量</div>
          <div class="stat-value">
            <img src="../assets/icon/register.png" />
            <span>{{ formatNumber(summary.total.register) }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card card4">
          <div class="stat-title">总入金</div>
          <div class="stat-value">
            <img src="../assets/icon/money.png" />
            <span>${{ formatNumber(summary.total.deposit) }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- KOL转化效果Top5 -->
    <el-card class="kol-table-card">
      <div class="kol-table-title">KOL转化效果Top 5（本月）</div>
      <el-table :data="kolTop5" class="kol-table" border>
        <el-table-column label="排名" type="index" width="60" />
        <el-table-column prop="kol_name" label="KOL" min-width="120" />
        <el-table-column prop="click_count" label="点击量" min-width="100" />
        <el-table-column prop="register_count" label="注册量" min-width="100" />
        <el-table-column prop="conversion_rate" label="注册转化率" min-width="120" />
        <el-table-column prop="deposit_amount" label="总入金" min-width="120">
          <template #default="scope">
            ${{ formatNumber(scope.row.deposit_amount) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </el-main>
</template>

  <script setup>
  import { ref, onMounted } from 'vue'

import apiService from '@/utils/api'
import { useUserStore } from '@/store/user'
  const summary = ref({
    task_count: 0,
    week: { click: 0, register: 0, deposit: 0, click_trend: 0, register_trend: 0, deposit_trend: 0 },
    month: { click: 0, register: 0, deposit: 0 },
    total: { click: 0, register: 0, deposit: 0 }
  })
  const kolTop5 = ref([])

  function formatNumber(val) {
    if (val == null) return '0'
    return val.toLocaleString()
  }
  function trendClass(val) {
    if (val > 0) return 'trend-up'
    if (val < 0) return 'trend-down'
    return ''
  }
  function tableHeaderStyle() {
    return { background: '#181818', color: '#fff', fontWeight: 'bold', fontSize: '15px' }
  }
  function tableCellStyle() {
    return { background: '#222', color: '#fff', fontSize: '15px' }
  }
  const userStore = useUserStore()
  async function fetchSummary() {
    // 检查是否已登录
    if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
      console.log('📈 User not logged in, skipping dashboard summary fetch')
      return
    }

    try {
      const res = await apiService.getDashboardSummary({ user_id: userStore.userInfo.id })
      // 兼容后端返回结构
      summary.value = {
        ...res.data,
        week: {
          ...res.data.week,
          click_trend: res.data.week.click_trend ?? 15.2,
          register_trend: res.data.week.register_trend ?? 8.7,
          deposit_trend: res.data.week.deposit_trend ?? -2.1
        }
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    }
  }

  async function fetchKOLTop5() {
    // 检查是否已登录
    if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
      console.log('🏆 User not logged in, skipping KOL top5 fetch')
      return
    }

    try {
      const res = await apiService.getDashboardKolTop5({ user_id: userStore.userInfo.id })
      kolTop5.value = res.data
      // kolTop5.value = [
      //   { kol_name: 'WMQ', click_count: 123, register_count: 3434, conversion_rate: '30%', deposit_amount: 2233 },
      //   { kol_name: 'WMQ', click_count: 123, register_count: 3434, conversion_rate: '30%', deposit_amount: 2233 },
      //   { kol_name: 'WMQ', click_count: 123, register_count: 3434, conversion_rate: '30%', deposit_amount: 2233 },
      //   { kol_name: 'WMQ', click_count: 123, register_count: 3434, conversion_rate: '30%', deposit_amount: 2233 },
      //   { kol_name: 'WMQ', click_count: 123, register_count: 3434, conversion_rate: '30%', deposit_amount: 2233 },
      // ];
    } catch (error) {
      console.error('获取KOL Top5数据失败:', error)
    }
  }

  onMounted(() => {
    fetchSummary()
    fetchKOLTop5()
  })
  </script>

  <style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }

  .c-title {
    color: #999;
    font-size: 14px;
    padding: 8px 0;
  }

  .count {
    margin-top: 32px;
  }

  .stat-card {
    min-height: 100px;
    border-radius: 8px;
    margin-bottom: 16px;
    color: #fff;
    background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

    &.card2 {
      background: linear-gradient(to bottom right, #e74888, #bc53a1);
    }

    &.card3 {
      background: linear-gradient(to bottom right, #825dbf, #5345b4);
    }

    &.card4 {
      background: linear-gradient(to bottom right, #fbb728, #f68254);
    }

    .stat-value {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 10px;

      img {
        width: 24px;
        height: 24px;
      }

      span {
        font-size: 32px;
      }
    }

    &.card1 {}
  }

  .kol-table-card {
    margin-top: 32px;
    padding: 16px 16px 24px 16px;
  }

  .kol-table-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .kol-table {
    /* border-radius: 16px; */
  }

  :deep(.el-table th) {
    font-weight: bold;
    font-size: 15px;
  }

  :deep(.el-table td) {
    font-size: 15px;
  }
</style>
