<template>
  <el-main>
    <div class="title">自动结算系统</div>
    <div class="c-title">查看并处理KOL的佣金结算</div>
    <div class="main">
      <div class="module-title">
        <span class="name">结算单</span>
        <el-button type="primary">一键支付所有待结算</el-button>
      </div>
      <el-table :data="settlements" border>
        <el-table-column prop="kol_name" label="KOL名称" />
        <el-table-column prop="task_name" label="任务名称" />
        <el-table-column prop="base_fee" label="基础费用">
          <template #default="scope">￥{{ formatMoney(scope.row.base_fee) }}</template>
        </el-table-column>
        <el-table-column prop="performance_fee" label="效果佣金">
          <template #default="scope">￥{{ formatMoney(scope.row.performance_fee) }}</template>
        </el-table-column>
        <el-table-column prop="total_fee" label="总计">
          <template #default="scope">￥{{ formatMoney(scope.row.total_fee) }}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'paid' ? 'success' : (scope.row.status === 'failed' ? 'danger' : 'warning')" effect="dark">
              {{ scope.row.status === 'paid' ? '已支付' : scope.row.status === 'pending' ? '待支付' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="settlement_date" label="结算日期">
          <template #default="scope">{{ scope.row.settlement_date || '-' }}</template>
        </el-table-column>
        <el-table-column prop="settlement_month" label="结算月份" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button v-if="scope.row.status === 'pending'" type="primary" size="small">单独支付</el-button>
            <el-button v-else size="small">查看明细</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-main>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import apiService from '@/utils/api'

const settlements = ref([])

function formatMoney(val) {
  return Number(val).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

async function fetchData() {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('💰 User not logged in, skipping settlement fetch')
    settlements.value = []
    return
  }

  try {
    const res = await apiService.getUserSettlements()
    // const res = {
    //   data: [
    //     {
    //       kol_name: 'wmq',
    //       task_name: '任务名称',
    //       base_fee: 123,
    //       performance_fee: 222,
    //       total_fee: 2121,
    //     },
    //     {
    //       kol_name: 'wmq',
    //       task_name: '任务名称',
    //       base_fee: 123,
    //       performance_fee: 222,
    //       total_fee: 2121,
    //     },
    //     {
    //       kol_name: 'wmq',
    //       task_name: '任务名称',
    //       base_fee: 123,
    //       performance_fee: 222,
    //       total_fee: 2121,
    //     }
    //   ]
    // };
    settlements.value = Array.isArray(res.data) ? res.data : [res.data]
  } catch (e) {
    console.error('获取结算数据失败:', e)
    settlements.value = []
  }
}

onMounted(fetchData)
</script>

<style scoped>
  .title {
    color: #444;
    font-size: 24px;
  }

  .c-title {
    color: #999;
    font-size: 14px;
    padding: 8px 0;
  }
  .main {
    background: #fff;
    margin-top: 32px;
    padding: 20px;
    .module-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      .name {
        font-size: 16px;
      }
    }
  }
</style>
