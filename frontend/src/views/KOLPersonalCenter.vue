<template>
  <el-main>
    <div style="color:#fff; font-size:22px; font-weight:bold; margin-bottom:8px;">个人中心</div>
    <div style="color:#aaa; font-size:15px; margin-bottom:24px;">管理您的个人信息和账号设置</div>

    <el-row :gutter="24">
      <el-col :span="24">
        <!-- 个人信息卡片 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">个人信息卡片</div>
          </template>
          <div class="user-info">
            <div class="info-item">
              <span class="info-label">👤 用户名:</span>
              <span class="info-value">{{ userInfo.username || 'CryptoExpert' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">📧 邮箱:</span>
              <span class="info-value">{{ maskEmail(userInfo.email) || 'crypto***@gmail.com' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">📅 注册时间:</span>
              <span class="info-value">{{ formatDate(userInfo.create_time) || '2024-01-15' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">🔔 KOL状态:</span>
              <span class="info-value status-active">✅已激活</span>
            </div>
            <div class="info-item">
              <span class="info-label">💰 收款地址:</span>
              <span class="info-value">{{ maskWalletAddress(userInfo.wallet_address) || '0x742d35...' }}</span>
            </div>
          </div>
        </el-card>

        <!-- Twitter OAuth绑定组件 -->
        <TwitterOAuthBinding />

        <!-- 专业标签 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">专业标签</div>
          </template>
          <div class="tags-section">
            <div class="tags-display">
              <el-tag
                v-for="tag in displayTags"
                :key="tag"
                type="primary"
                size="large"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ tag }}
              </el-tag>
              <el-button size="small" @click="showEditTags = true" style="margin-left: 8px;">编辑</el-button>
            </div>

            <div class="description-section" style="margin-top: 20px;">
              <div class="info-label">📝 个人简介:</div>
              <div class="description-text">
                {{ kolProfile.description || '资深DeFi分析师，专注于去中心化金融协议研究和投资策略...' }}
              </div>
              <el-button size="small" @click="showEditDescription = true" style="margin-top: 8px;">编辑</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 编辑标签弹窗 -->
    <el-dialog v-model="showEditTags" title="编辑专业标签" width="500px" class="edit-dialog">
      <div style="margin-bottom: 20px;">
        <div style="color: #fff; margin-bottom: 10px;">当前标签:</div>
        <el-tag
          v-for="tag in selectedTags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          style="width: 120px;"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
      </div>

      <div>
        <div style="color: #fff; margin-bottom: 10px;">可选标签:</div>
        <el-tag
          v-for="tag in availableTags"
          :key="tag"
          :type="selectedTags.includes(tag) ? 'success' : 'info'"
          @click="toggleTag(tag)"
          style="margin-right: 8px; margin-bottom: 8px; cursor: pointer;"
        >
          {{ tag }}
        </el-tag>
      </div>

      <div style="color: #909399; font-size: 12px; margin-top: 10px;">
        最多选择3个标签，可自定义填写
      </div>

      <template #footer>
        <el-button @click="showEditTags = false">取消</el-button>
        <el-button type="primary" @click="saveTags" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 编辑简介弹窗 -->
    <el-dialog v-model="showEditDescription" title="编辑个人简介" width="500px" class="edit-dialog">
      <el-input
        v-model="editDescription"
        type="textarea"
        :rows="6"
        placeholder="请简单介绍您的专业领域、经验和内容创作能力..."
        maxlength="500"
        show-word-limit
      />

      <template #footer>
        <el-button @click="showEditDescription = false">取消</el-button>
        <el-button type="primary" @click="saveDescription" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </el-main>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import apiService from '@/utils/api'
import { ElMessage } from 'element-plus'
import TwitterOAuthBinding from '@/components/TwitterOAuthBinding.vue'

const userInfo = ref({})
const kolProfile = ref({})
const saving = ref(false)
const showEditTags = ref(false)
const showEditDescription = ref(false)
const editDescription = ref('')

// 标签相关
const selectedTags = ref([])
const inputVisible = ref(false)
const inputValue = ref('')
const inputRef = ref()

const availableTags = [
  'DeFi', 'NFT', 'GameFi', 'Layer2', '交易', '投资',
  '技术分析', '项目评测', '教育科普', '新闻资讯'
]

const displayTags = computed(() => {
  if (kolProfile.value.tag_name) {
    return kolProfile.value.tag_name.split(', ').filter(tag => tag.trim())
  }
  return ['DeFi', '技术分析', '投资策略']
})

// 获取用户信息
async function fetchUserInfo() {
  try {
    const stored = localStorage.getItem('userInfo')
    if (stored) {
      userInfo.value = JSON.parse(stored)
    }

    // 检查是否已登录
    if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
      console.log('👤 User not logged in, skipping KOL profile fetch')
      // 显示默认信息用于演示
      kolProfile.value = {
        platform_username: 'cryptoexpert',
        followers_count: 125678,
        following_count: 1234,
        tweet_count: 5678,
        verified: true,
        last_synced_at: new Date().toISOString(),
        tag_name: 'DeFi, 技术分析, 投资策略',
        description: '资深DeFi分析师，专注于去中心化金融协议研究和投资策略...'
      }
      selectedTags.value = displayTags.value
      editDescription.value = kolProfile.value.description || ''
      return
    }

    // 获取KOL详细信息
    const response = await apiService.getKolProfile()
    kolProfile.value = response.data
    selectedTags.value = displayTags.value
    editDescription.value = kolProfile.value.description || ''
  } catch (error) {
    console.error('获取用户信息失败:', error)
    if (error.response?.status === 404) {
      // 如果没有KOL资料，显示提示信息
      ElMessage.warning('请先完善KOL资料信息')
      kolProfile.value = {
        platform_username: '',
        followers_count: 0,
        following_count: 0,
        tweet_count: 0,
        verified: false,
        last_synced_at: null,
        tag_name: '',
        description: ''
      }
    } else {
      // 显示默认信息用于演示
      kolProfile.value = {
        platform_username: 'cryptoexpert',
        followers_count: 125678,
        following_count: 1234,
        tweet_count: 5678,
        verified: true,
        last_synced_at: new Date().toISOString(),
        tag_name: 'DeFi, 技术分析, 投资策略',
        description: '资深DeFi分析师，专注于去中心化金融协议研究和投资策略...'
      }
    }
    selectedTags.value = displayTags.value
    editDescription.value = kolProfile.value.description || ''
  }
}

// Twitter数据现在由TwitterOAuthBinding组件处理

// 标签管理
function toggleTag(tag) {
  if (selectedTags.value.includes(tag)) {
    removeTag(tag)
  } else if (selectedTags.value.length < 3) {
    selectedTags.value.push(tag)
  } else {
    ElMessage.warning('最多只能选择3个标签')
  }
}

function removeTag(tag) {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  }
}

function showInput() {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function handleInputConfirm() {
  if (inputValue.value && selectedTags.value.length < 3) {
    if (!selectedTags.value.includes(inputValue.value)) {
      selectedTags.value.push(inputValue.value)
    }
  } else if (selectedTags.value.length >= 3) {
    ElMessage.warning('最多只能选择3个标签')
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 保存标签
async function saveTags() {
  if (selectedTags.value.length === 0) {
    ElMessage.warning('请至少选择一个标签')
    return
  }

  saving.value = true
  try {
    await apiService.updateKolProfile({
      tag_name: selectedTags.value.join(', ')
    })
    kolProfile.value.tag_name = selectedTags.value.join(', ')
    showEditTags.value = false
    ElMessage.success('标签保存成功！')
  } catch (error) {
    console.error('保存标签失败:', error)
    ElMessage.error(error.response?.data?.detail || '保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 保存简介
async function saveDescription() {
  if (!editDescription.value.trim()) {
    ElMessage.warning('请输入个人简介')
    return
  }

  saving.value = true
  try {
    await apiService.updateKolProfile({
      description: editDescription.value
    })
    kolProfile.value.description = editDescription.value
    showEditDescription.value = false
    ElMessage.success('简介保存成功！')
  } catch (error) {
    console.error('保存简介失败:', error)
    ElMessage.error(error.response?.data?.detail || '保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 工具函数
function maskEmail(email) {
  if (!email) return ''
  const [username, domain] = email.split('@')
  if (username.length <= 3) return email
  return username.slice(0, 3) + '***@' + domain
}

function maskWalletAddress(address) {
  if (!address) return ''
  if (address.length <= 10) return address
  return address.slice(0, 6) + '...' + address.slice(-4)
}

function formatNumber(num) {
  return num ? num.toLocaleString() : '0'
}

function formatDate(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

function formatDateTime(dateStr) {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
.info-card {
  background: #23242a;
  border: 1px solid #555;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.info-card :deep(.el-card__header) {
  background: #23242a;
  border-bottom: 1px solid #555;
  color: #fff;
}

.info-card :deep(.el-card__body) {
  background: #23242a;
  color: #fff;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.user-info, .twitter-info, .tags-section {
  color: #fff;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
}

.info-label {
  min-width: 120px;
  color: #aaa;
  font-weight: 500;
}

.info-value {
  color: #fff;
  flex: 1;
}

.status-active {
  color: #4CAF50 !important;
}

.tags-display {
  margin-bottom: 16px;
}

.description-section {
  border-top: 1px solid #555;
  padding-top: 16px;
}

.description-text {
  background: #2a2b32;
  border: 1px solid #555;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  color: #fff;
  line-height: 1.5;
}

/* 弹窗样式 */
.edit-dialog :deep(.el-dialog) {
  background: #23242a;
  border: 1px solid #555;
}

.edit-dialog :deep(.el-dialog__header) {
  background: #23242a;
  border-bottom: 1px solid #555;
}

.edit-dialog :deep(.el-dialog__title) {
  color: #fff;
}

.edit-dialog :deep(.el-dialog__body) {
  background: #23242a;
  color: #fff;
}

.edit-dialog :deep(.el-dialog__footer) {
  background: #23242a;
  border-top: 1px solid #555;
}

/* 表单样式 */
:deep(.el-input__wrapper) {
  background: #181818 !important;
  border-color: #555 !important;
}

:deep(.el-input__inner) {
  background: #181818 !important;
  color: #fff !important;
}

:deep(.el-textarea__inner) {
  background: #181818 !important;
  color: #fff !important;
  border-color: #555 !important;
}

/* 标签样式 */
:deep(.el-tag) {
  margin-right: 8px;
  margin-bottom: 8px;
}

:deep(.el-tag--info) {
  background: #2a2b32;
  border-color: #555;
  color: #fff;
}

:deep(.el-tag--success) {
  background: #4CAF50;
  border-color: #4CAF50;
  color: #fff;
}

:deep(.el-tag--primary) {
  background: #409EFF;
  border-color: #409EFF;
  color: #fff;
}

/* 按钮样式 */
:deep(.el-button) {
  border-color: #555 !important;
}

:deep(.el-button--primary) {
  background: #409EFF !important;
  border-color: #409EFF !important;
}
</style>
