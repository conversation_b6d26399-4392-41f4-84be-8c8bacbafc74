<template>
  <el-main>
    <div class="title">
      <span>管理所有KOL营销任务的生命周期</span>
      <el-button type="primary" class="create-btn" @click="showCreateTaskDialog">创建新任务</el-button>
    </div>
    <div>
      <div class="table-container">
        <el-table :data="tasks" border stripe highlight-current-row>

          <!-- 任务名称 -->
          <el-table-column prop="task_name" label="任务名称" show-overflow-tooltip>
            <template #default="scope">
              <div class="task-name">
                <div class="task-content">
                  <span class="task-title">{{ scope.row.task_name }}</span>
                  <span class="task-id">ID: {{ scope.row.id }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- KOL姓名 -->
          <el-table-column prop="kol_username" label="KOL姓名" align="center">
            <template #default="scope">
              <div class="kol-name">
                <div v-if="scope.row.kol_username" class="kol-assigned">
                  <span class="username">{{ scope.row.kol_username }}</span>
                </div>
                <div v-else class="kol-unassigned">
                  <span class="no-kol">暂未分配</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 任务状态 -->
          <el-table-column prop="task_status" label="任务状态" align="center">
            <template #default="scope">
              <el-tag :type="statusType(scope.row.task_status)" effect="dark">
                {{ getStatusDisplayName(scope.row.task_status) }}
              </el-tag>
            </template>
          </el-table-column>



          <!-- 任务类型 -->
          <el-table-column prop="task_type" label="任务类型" align="center">
            <template #default="scope">
              <div class="task-type">
                <el-tag size="small" :type="getTaskTypeColor(scope.row.task_type)" effect="dark" class="type-tag">
                  <el-icon class="type-icon">
                    <component :is="getTaskTypeIcon(scope.row.task_type)" />
                  </el-icon>
                  <span>&nbsp;{{ getTaskTypeDisplayName(scope.row.task_type) }}</span>
                </el-tag>
              </div>
            </template>
          </el-table-column>


          <!-- 奖励类型 -->
          <el-table-column prop="reward_type" label="奖励类型" align="center">
            <template #default="scope">
              <div class="reward-type-display">
                <el-tag size="small" :type="getRewardTypeColor(scope.row.reward_type)" effect="dark"
                  class="reward-type-tag">
                  <el-icon class="reward-type-icon">
                    <component :is="getRewardTypeIcon(scope.row.reward_type)" />
                  </el-icon>
                  {{ getRewardTypeDisplayName(scope.row.reward_type) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 开始日期 -->
          <el-table-column prop="start_date" label="开始日期" align="center">
            <template #default="scope">
              <div class="date-display">
                <span class="date-text">{{ formatDate(scope.row.start_date) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 结束日期 -->
          <el-table-column prop="end_date" label="结束日期" align="center">
            <template #default="scope">
              <div class="date-display">
                <span class="date-text">{{ formatDate(scope.row.end_date) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 任务详情 -->
          <el-table-column label="任务详情" width="120" align="center">
            <template #default="scope">
              <!-- 草稿状态显示编辑按钮 -->
              <el-button
                v-if="scope.row.task_status === 'draft'"
                type="primary"
                size="small"
                @click="editDraft(scope.row)"
                class="action-btn compact action-btn--primary">
                <el-icon>
                  <Edit />
                </el-icon>
                编辑草稿
              </el-button>
              <!-- 其他状态显示查看详情按钮 -->
              <el-button
                v-else
                type="info"
                size="small"
                @click="showTaskDetail(scope.row)"
                class="action-btn compact action-btn--info">
                <el-icon>
                  <View />
                </el-icon>
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </div>
    </div>



    <!-- 任务详情对话框 -->
    <el-drawer v-model="taskDetailVisible" title="任务详情" size="50%" :before-close="() => taskDetailVisible = false"
      class="task-detail-dialog">
      <TaskDetailDialog v-if="currentDetailTask" :task="currentDetailTask" @close="taskDetailVisible = false" @status-changed="handleTaskStatusChanged" />
    </el-drawer>

    <!-- 创建/编辑任务对话框 -->
    <el-drawer
      v-model="createTaskVisible"
      :title="isEditMode ? '编辑草稿' : '创建新任务'"
      size="50%"
      :before-close="() => createTaskVisible = false"
      class="create-task-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="true">
      <el-form :model="newTask" :rules="taskRules" ref="taskFormRef" class="create-task-form" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="form-header">
            <span class="section-title">基本信息</span>
          </div>

          <el-form-item label="任务名称" prop="task_name" class="form-item">
            <el-input v-model="newTask.task_name" placeholder="请输入任务名称" class="form-input" size="large"
              style="width: 360px;" @input="checkTaskNameAvailability"
              :class="{ 'is-available': taskNameAvailable, 'is-unavailable': !taskNameAvailable && newTask.task_name }" />
            <div v-if="newTask.task_name" class="task-name-status">
              <el-icon v-if="taskNameAvailable" class="status-icon available">
                <CircleCheck />
              </el-icon>
              <el-icon v-else class="status-icon unavailable">
                <CircleClose />
              </el-icon>
              <span :class="{ 'available': taskNameAvailable, 'unavailable': !taskNameAvailable }">
                {{ taskNameAvailable ? '任务名称可用' : '任务名称已存在' }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="任务类型" prop="task_type" class="form-item">
            <el-select style="width: 360px;" v-model="newTask.task_type" placeholder="请选择任务类型" class="form-select"
              size="large">
              <el-option label="推文" value="post">
                <el-icon>
                  <ChatDotRound />
                </el-icon>
                <span>推文</span>
              </el-option>
              <el-option label="视频" value="video">
                <el-icon>
                  <VideoPlay />
                </el-icon>
                <span>视频</span>
              </el-option>
              <el-option label="文章" value="article">
                <el-icon>
                  <Document />
                </el-icon>
                <span>文章</span>
              </el-option>
              <el-option label="直播" value="live_stream">
                <el-icon>
                  <VideoCamera />
                </el-icon>
                <span>直播</span>
              </el-option>
              <el-option label="AMA活动" value="ama_activity">
                <el-icon>
                  <User />
                </el-icon>
                <span>AMA活动</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开始日期" prop="start_date" class="form-item">
            <el-date-picker v-model="newTask.start_date" type="date" placeholder="选择开始日期" class="form-date-picker"
              size="large" />
          </el-form-item>
          <el-form-item label="截止日期" prop="end_date" class="form-item">
            <el-date-picker v-model="newTask.end_date" type="date" placeholder="选择截止日期" class="form-date-picker"
              size="large" />
          </el-form-item>
        </div>

        <!-- 奖励设置 -->
        <div class="form-section">
          <div class="form-header">
            <span class="section-title">奖励设置</span>
          </div>

          <el-form-item label="奖励类型" prop="reward_type" class="form-item">
            <el-radio-group v-model="newTask.reward_type" class="reward-type-group">
              <el-radio label="branding" class="reward-radio">
                <div class="reward-option">
                  <div class="reward-content">
                    <div class="reward-title">品牌推广</div>
                    <div class="reward-desc">(固定金额奖励)</div>
                  </div>
                </div>
              </el-radio>
              <el-radio label="commission" class="reward-radio">
                <div class="reward-option">
                  <div class="reward-content">
                    <div class="reward-title">带单返佣</div>
                    <div class="reward-desc">(纯佣金模式)</div>
                  </div>
                </div>
              </el-radio>
              <el-radio label="branding_plus_conversion" class="reward-radio">
                <div class="reward-option">
                  <div class="reward-content">
                    <div class="reward-title">品牌推广+按转化付费</div>
                    <div class="reward-desc">(基础奖励+FTT转化)</div>
                  </div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 基础奖励设置 -->
          <el-form-item label="基础奖励" prop="base_reward" class="form-item" v-if="newTask.reward_type !== 'commission'">
            <el-input-number v-model="newTask.base_reward" :min="0" :precision="2" class="form-input-number"
              size="large" controls-position="right" placeholder="请输入基础奖励金额" style="width: 240px;">
              <template #prefix>
                <span class="currency-prefix">$</span>
              </template>
            </el-input-number>
            <div class="field-hint" style="margin-left: 8px;">
              <el-icon class="hint-icon">
                <InfoFilled />
              </el-icon>
              基础推广费用，品牌推广模式必填
            </div>
          </el-form-item>

          <!-- 效果奖励设置 - 只在非品牌推广模式下显示 -->
          <el-form-item label="效果奖励" prop="performance_rate" class="form-item" v-if="newTask.reward_type !== 'branding'">
            <el-input-number style="width: 240px;" v-model="newTask.performance_rate" :min="0" :precision="2"
              class="form-input-number" size="large" controls-position="right">
              <template #prefix>
                <span class="currency-prefix">$</span>
              </template>
            </el-input-number>
            <div class="field-hint" style="margin-left: 8px;">
              <el-icon class="hint-icon">
                <InfoFilled />
              </el-icon>
              <span v-if="newTask.reward_type === 'commission'">带单返佣比例</span>
              <span v-else-if="newTask.reward_type === 'branding_plus_conversion'">按转化付费单价</span>
            </div>
          </el-form-item>
        </div>

        <!-- 任务详情 -->
        <div class="form-section">
          <div class="form-header">
            <span class="section-title">任务详情</span>
          </div>

          <el-form-item label="任务描述" prop="description" class="form-item">
            <el-input v-model="newTask.description" type="textarea" :rows="5" placeholder="请输入任务描述、包含任务发布要求"
              class="form-textarea" resize="none" />
          </el-form-item>

          <el-form-item label="官方素材" prop="official_materials" class="form-item">
            <div class="materials-section">
              <el-button type="primary" @click="showMaterialUpload = true" class="upload-btn">
                <el-icon><Upload /></el-icon>
                上传素材
              </el-button>
              <div class="materials-preview" v-if="materials.length > 0">
                <div v-for="(material, index) in materials" :key="index" class="material-item">
                  <!-- 图片预览 -->
                  <div v-if="material.type === 'image'" class="material-image">
                    <el-image
                      :src="material.url"
                      :preview-src-list="[material.url]"
                      fit="cover"
                      class="preview-image"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                    <div class="material-info">
                      <span class="material-name">{{ material.name }}</span>
                      <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 视频预览 -->
                  <div v-else-if="material.type === 'video'" class="material-video">
                    <video
                      :src="material.url"
                      controls
                      preload="metadata"
                      class="video-player"
                    ></video>
                    <div class="material-info">
                      <span class="material-name">{{ material.name }}</span>
                      <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 链接预览 -->
                  <div v-else class="material-link">
                    <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                      {{ material.name || material.url }}
                    </el-link>
                    <el-button type="danger" size="small" @click="removeMaterial(index)" class="remove-btn">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelEdit" size="large">取消</el-button>
          <el-button
            type="primary"
            @click="isEditMode ? updateDraft() : createTask()"
            :loading="creating"
            size="large">
            {{ isEditMode ? '保存草稿' : '创建任务' }}
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 素材上传对话框 -->
    <MaterialUploadDialog
      v-model:visible="showMaterialUpload"
      @materials-confirmed="handleMaterialsConfirmed"
    />
  </el-main>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElDialog, ElMessageBox } from 'element-plus'
import { Document, Link, ChatDotRound, VideoPlay, VideoCamera, User, Money, Edit, CircleCheck, CircleClose, View, UserFilled, Calendar, Clock, Star, TrendCharts, InfoFilled, Upload, Picture, Delete } from '@element-plus/icons-vue'
import apiService from '@/utils/api'

import TaskDetailDialog from '../components/TaskDetailDialog.vue'
import MaterialUploadDialog from '../components/MaterialUploadDialog.vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore() // 用户状态管理
const tasks = ref([]) // 任务列表

// 分页相关
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 编辑模式相关状态
const isEditMode = ref(false) // 是否为编辑模式
const editingTaskId = ref(null) // 正在编辑的任务ID
const originalTaskData = ref(null) // 原始任务数据备份

const createTaskVisible = ref(false)
const creating = ref(false)
const taskFormRef = ref(null)
const taskNameAvailable = ref(true) // 任务名称是否可用
const checkTaskNameTimeout = ref(null) // 防抖计时器
const taskDetailVisible = ref(false) // 任务详情对话框显示状态
const currentDetailTask = ref(null) // 当前查看详情的任务
const showMaterialUpload = ref(false) // 素材上传对话框显示状态
const materials = ref([]) // 素材列表

// 新任务表单数据
const newTask = ref({
  task_name: '',
  task_type: 'post',
  start_date: null,
  end_date: null,
  description: '',

  // 奖励相关字段
  reward_type: 'branding',
  base_reward: 0,
  performance_rate: 0,
  commission_rate: 0,
  //conversion_reward_per_ftt: 0,
  enable_conversion_reward: false,

  official_materials: ''
})

// 表单验证规则
const taskRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        // 检查任务名称是否重复
        const existingTask = tasks.value.find(task =>
          task.task_name.toLowerCase() === value.toLowerCase()
        )
        if (existingTask) {
          callback(new Error('任务名称已存在，请使用其他名称'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  task_type: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: '请选择截止日期', trigger: 'change' }
  ],
  base_reward: [
    { required: true, message: '请输入基础奖励', trigger: 'blur' }
  ]
}



// 显示任务详情对话框
const showTaskDetail = (task) => {
  console.log('=== showTaskDetail Debug ===')
  console.log('Selected task:', task)
  console.log('Task ID:', task.id)
  console.log('Task name:', task.task_name)

  currentDetailTask.value = { ...task } // 创建任务对象的副本

  console.log('currentDetailTask after assignment:', currentDetailTask.value)
  console.log('currentDetailTask ID:', currentDetailTask.value.id)
  console.log('Setting taskDetailVisible to true...')

  taskDetailVisible.value = true

  console.log('taskDetailVisible is now:', taskDetailVisible.value)
  console.log('=== End showTaskDetail Debug ===')

  taskDetailVisible.value = true

  console.log('Test task set, dialog should open')
}

// 处理任务状态变更
const handleTaskStatusChanged = ({ taskId, oldStatus, newStatus }) => {
  console.log('TaskCenter: Status changed:', { taskId, oldStatus, newStatus })

  // 找到并更新特定任务的状态
  const taskIndex = tasks.value.findIndex(task => task.id === taskId)
  if (taskIndex !== -1) {
    // 创建任务对象的副本并更新状态
    const updatedTask = { ...tasks.value[taskIndex], task_status: newStatus }
    tasks.value.splice(taskIndex, 1, updatedTask)
    console.log('Updated task status in TaskCenter:', newStatus)
  }

  // 更新当前详情任务的状态
  if (currentDetailTask.value && currentDetailTask.value.id === taskId) {
    currentDetailTask.value.task_status = newStatus
    console.log('Updated current detail task status to:', newStatus)
  }
}


// 显示创建任务对话框
const showCreateTaskDialog = () => {
  // 重置编辑模式
  resetEditMode()

  // 使用 nextTick 确保 DOM 更新完成后再显示对话框
  nextTick(() => {
    createTaskVisible.value = true
    // 重置表单
    newTask.value = {
      task_name: '',
      task_type: 'post',
      start_date: null,
      end_date: null,
      description: '',

      // 奖励相关字段
      reward_type: 'branding',
      base_reward: 0,
      performance_rate: 0,
      commission_rate: 0,
      //conversion_reward_per_ftt: 0,
      enable_conversion_reward: false,

      official_materials: ''
    }
    materials.value = []
    taskNameAvailable.value = true // 重置可用性状态
  })
}

// 创建任务
const createTask = async () => {
  try {
    await taskFormRef.value.validate()
    creating.value = true

    const taskData = {
      ...newTask.value,
      start_date: newTask.value.start_date ? newTask.value.start_date.toISOString().split('T')[0] : null,
      end_date: newTask.value.end_date ? newTask.value.end_date.toISOString().split('T')[0] : null,
      creator: userStore.userInfo?.id || 1, // 使用当前登录用户ID
      task_status: 'draft',
      target_kol_count: 1, // 默认目标KOL数量
      // 确保所有字段都有值
      reward_type: newTask.value.reward_type || 'branding',
      base_reward: newTask.value.reward_type === 'commission' ? 0 : (newTask.value.base_reward || 0),
      performance_rate: newTask.value.performance_rate || 0,
      commission_rate: newTask.value.commission_rate || 0,
      //conversion_reward_per_ftt: newTask.value.conversion_reward_per_ftt || 0,
      enable_conversion_reward: newTask.value.reward_type === 'branding_plus_conversion' ? true : (newTask.value.enable_conversion_reward || false),
      official_materials: materials.value, // 直接传递数组，不进行JSON.stringify
      draft_content: '' // 草稿内容为空
    }

    const response = await apiService.createMarketingTask(taskData)

    if (response.data.msg) {
      ElMessage.success(response.data.msg)
      createTaskVisible.value = false
      fetchTasks() // 刷新任务列表
    } else {
      ElMessage.error('创建失败，请重试')
    }
  } catch (error) {
    console.error('创建任务失败:', error)

    // 处理不同类型的错误
    if (error.response?.status === 400) {
      const errorDetail = error.response.data.detail
      if (errorDetail && errorDetail.includes('已存在')) {
        // 任务名称重复错误
        ElMessage.error({
          message: errorDetail,
          duration: 5000,
          showClose: true
        })
        // 聚焦到任务名称输入框
        setTimeout(() => {
          const taskNameInput = document.querySelector('input[placeholder="请输入任务名称"]')
          if (taskNameInput) {
            taskNameInput.focus()
            taskNameInput.select()
          }
        }, 100)
      } else {
        ElMessage.error(errorDetail || '创建失败，请检查输入数据')
      }
    } else if (error.response?.status === 422) {
      // 表单验证错误
      ElMessage.error('请检查表单数据，确保所有必填字段都已填写')
    } else if (error.response?.status === 500) {
      ElMessage.error('服务器错误，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
  } finally {
    creating.value = false
  }
}

// 监听奖励类型变化
watch(() => newTask.value.reward_type, (newType) => {
  if (newType === 'commission') {
    // 带单返佣模式时，基础奖励设为0
    newTask.value.base_reward = 0
  } else if (newType === 'branding_plus_conversion') {
    // 品牌推广+按转化付费模式时，按转化付费为必选
    newTask.value.enable_conversion_reward = true
  }
})

// 获取任务列表
const fetchTasks = async () => {
  // 检查是否已登录
  if (!localStorage.getItem('token') || localStorage.getItem('isLoggedIn') !== 'true') {
    console.log('📋 User not logged in, skipping task fetch')
    tasks.value = []
    pagination.value.total = 0
    return
  }

  try {
    const params = {
      page: pagination.value.currentPage,
      page_size: pagination.value.pageSize
    }
    
    const res = await apiService.getBitMarketingTasks(params)
    console.log('=== fetchTasks Debug ===')
    console.log('Raw API response:', res.data)

    if (res.data && res.data.items) {
      // 分页数据格式
      tasks.value = res.data.items.map(task => ({ ...task }))
      pagination.value.total = res.data.total || 0
    } else if (Array.isArray(res.data)) {
      // 兼容旧格式，所有数据
      tasks.value = res.data.sort((a, b) => {
        return new Date(b.create_time) - new Date(a.create_time)
      }).map(task => ({ ...task }))
      pagination.value.total = res.data.length
    } else if (res.data) {
      tasks.value = [{ ...res.data }]
      pagination.value.total = 1
    } else {
      tasks.value = []
      pagination.value.total = 0
    }
    
    console.log('Processed tasks:', tasks.value.map(task => ({
      id: task.id,
      name: task.task_name,
      status: task.task_status
    })))
    console.log('Pagination info:', pagination.value)
    console.log('=== End fetchTasks Debug ===')
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
    tasks.value = []
    pagination.value.total = 0
  }
}

onMounted(fetchTasks)

// 分页事件处理
const handleSizeChange = (newSize) => {
  pagination.value.pageSize = newSize
  pagination.value.currentPage = 1 // 重置到第一页
  fetchTasks()
}

const handleCurrentChange = (newPage) => {
  pagination.value.currentPage = newPage
  fetchTasks()
}

// 状态类型映射
const statusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'published': 'warning',
    'assigned': 'primary',
    'unapproved': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'running': 'success',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 状态显示名称映射
const getStatusDisplayName = (status) => {
  const statusMap = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '已分配',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝待修改',
    'running': '运行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 任务类型显示名称映射
const getTaskTypeDisplayName = (type) => {
  const typeMap = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA活动'
  }
  return typeMap[type] || type
}

// 任务类型图标映射
const getTaskTypeIcon = (type) => {
  const iconMap = {
    'post': ChatDotRound,
    'video': VideoPlay,
    'article': Document,
    'live_stream': VideoCamera,
    'ama_activity': User
  }
  return iconMap[type] || ChatDotRound // 默认图标
}

// 任务类型颜色映射
const getTaskTypeColor = (type) => {
  const colorMap = {
    'post': 'info',
    'video': 'warning',
    'article': 'success',
    'live_stream': 'danger',
    'ama_activity': 'primary'
  }
  return colorMap[type] || 'info' // 默认颜色
}

// 奖励类型显示名称映射
const getRewardTypeDisplayName = (type) => {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌+转化'
  }
  return typeMap[type] || type
}

// 奖励类型图标映射
const getRewardTypeIcon = (type) => {
  const iconMap = {
    'branding': Star,
    'commission': Money,
    'branding_plus_conversion': TrendCharts
  }
  return iconMap[type] || Star // 默认图标
}

// 奖励类型颜色映射
const getRewardTypeColor = (type) => {
  const colorMap = {
    'branding': 'info',
    'commission': 'success',
    'branding_plus_conversion': 'warning'
  }
  return colorMap[type] || 'info' // 默认颜色
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化日期（仅日期）
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 检查任务名称是否可用
const checkTaskNameAvailability = async () => {
  if (!newTask.value.task_name) {
    taskNameAvailable.value = true // 清空时视为可用
    return
  }

  // 防抖处理，避免频繁请求
  clearTimeout(checkTaskNameTimeout.value)
  checkTaskNameTimeout.value = setTimeout(async () => {
    try {
      // 编辑模式下传递task_id参数，排除当前任务
      const params = isEditMode.value ? { task_id: editingTaskId.value } : {}
      const response = await apiService.checkTaskName(newTask.value.task_name, params)
      taskNameAvailable.value = response.data.available
    } catch (error) {
      console.error('检查任务名称可用性失败:', error)
      // 如果检查失败，使用本地验证
      const existingTask = tasks.value.find(task => {
        // 编辑模式下排除当前任务
        if (isEditMode.value && task.id === editingTaskId.value) {
          return false
        }
        return task.task_name.toLowerCase() === newTask.value.task_name.toLowerCase()
      })
      taskNameAvailable.value = !existingTask
    }
  }, 500) // 500ms 防抖延迟
}

// 查看草稿内容
const viewDraft = (task) => {
  ElMessageBox.alert(task.draft_content, '草稿内容', {
    confirmButtonText: '确定',
    customClass: 'dark-dialog'
  })
}

// 查看发布链接
const viewPublishedLinks = (links) => {
  try {
    const linkArray = JSON.parse(links)
    const linkText = Array.isArray(linkArray) ? linkArray.join('\n') : links
    ElMessageBox.alert(linkText, '发布链接', {
      confirmButtonText: '确定',
      customClass: 'dark-dialog'
    })
  } catch (error) {
    ElMessageBox.alert(links, '发布链接', {
      confirmButtonText: '确定',
      customClass: 'dark-dialog'
    })
  }
}

// 处理素材确认
const handleMaterialsConfirmed = (newMaterials) => {
  console.log('handleMaterialsConfirmed called, newMaterials:', newMaterials)
  materials.value = newMaterials // 直接替换，避免重复添加
  console.log('materials.value after update:', materials.value)
  showMaterialUpload.value = false
}

// 移除素材
const removeMaterial = (index) => {
  materials.value.splice(index, 1)
}

// 打开官方素材
const openMaterials = (materials) => {
  if (!materials) {
    ElMessage.warning('暂无官方素材')
    return
  }

  const materialLinks = materials.split(',').map(link => link.trim())
  const linkText = materialLinks.join('\n')
  ElMessageBox.alert(linkText, '官方素材链接', {
    confirmButtonText: '确定',
    customClass: 'dark-dialog'
  })
}

// 确保URL是完整的
const ensureFullUrl = (url) => {
  if (!url) return ''
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  return `https://${url}`
}

// 编辑草稿函数
const editDraft = (task) => {
  console.log('编辑草稿:', task)

  // 设置编辑模式
  isEditMode.value = true
  editingTaskId.value = task.id
  originalTaskData.value = { ...task }

  // 预填充表单数据
  newTask.value = {
    task_name: task.task_name || '',
    task_type: task.task_type || 'post',
    start_date: task.start_date ? new Date(task.start_date) : null,
    end_date: task.end_date ? new Date(task.end_date) : null,
    description: task.description || '',
    reward_type: task.reward_type || 'branding',
    base_reward: task.base_reward || 0,
    performance_rate: task.performance_rate || 0,
    commission_rate: task.commission_rate || 0,
    enable_conversion_reward: task.enable_conversion_reward || false,
    official_materials: ''
  }

  // 处理官方素材数据
  if (task.official_materials) {
    try {
      const materialsData = typeof task.official_materials === 'string'
        ? JSON.parse(task.official_materials)
        : task.official_materials
      materials.value = Array.isArray(materialsData) ? materialsData : []
    } catch (error) {
      console.error('解析官方素材失败:', error)
      materials.value = []
    }
  } else {
    materials.value = []
  }

  // 重置表单验证状态
  taskNameAvailable.value = true

  // 显示对话框
  nextTick(() => {
    createTaskVisible.value = true
  })
}

// 更新草稿函数
const updateDraft = async () => {
  try {
    await taskFormRef.value.validate()
    creating.value = true

    const taskData = {
      id: editingTaskId.value,
      ...newTask.value,
      start_date: newTask.value.start_date ? newTask.value.start_date.toISOString().split('T')[0] : null,
      end_date: newTask.value.end_date ? newTask.value.end_date.toISOString().split('T')[0] : null,
      // 确保所有字段都有值
      reward_type: newTask.value.reward_type || 'branding',
      base_reward: newTask.value.reward_type === 'commission' ? 0 : (newTask.value.base_reward || 0),
      performance_rate: newTask.value.performance_rate || 0,
      commission_rate: newTask.value.commission_rate || 0,
      enable_conversion_reward: newTask.value.reward_type === 'branding_plus_conversion' ? true : (newTask.value.enable_conversion_reward || false),
      official_materials: materials.value, // 直接传递数组
    }

    // 调用更新API
    const response = await apiService.updateMarketingTask(taskData)

    if (response.data.msg) {
      ElMessage.success('草稿保存成功')
      createTaskVisible.value = false
      resetEditMode()
      fetchTasks() // 刷新任务列表
    } else {
      ElMessage.error('保存失败，请重试')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)

    // 处理不同类型的错误
    if (error.response?.status === 400) {
      const errorDetail = error.response.data.detail
      ElMessage.error(errorDetail || '保存失败，请检查输入数据')
    } else if (error.response?.status === 422) {
      ElMessage.error('请检查表单数据，确保所有必填字段都已填写')
    } else if (error.response?.status === 500) {
      ElMessage.error('服务器错误，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
  } finally {
    creating.value = false
  }
}

// 取消编辑函数
const cancelEdit = () => {
  createTaskVisible.value = false
  resetEditMode()
}

// 重置编辑模式
const resetEditMode = () => {
  isEditMode.value = false
  editingTaskId.value = null
  originalTaskData.value = null
  materials.value = []
}


</script>

<style scoped>
.title {
  color: #444;
  font-size: 24px;
  display: flex;
  justify-content: space-between;
  padding-bottom: 32px;
}
/* 表格容器样式 */
.table-container {
  width: 100%;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为各个组件添加微妙的进入动画 */
.task-name, .kol-name, .status-tag, .action-buttons, .task-type, .date-display {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn.compact {
    padding: 2px 6px;
    font-size: 11px;
  }
}


/* 任务名称样式 */
.task-name {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.task-icon {
  background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.task-icon .el-icon {
  font-size: 16px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.task-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
}

.task-id {
  color: #909399;
  font-size: 11px;
  font-weight: 400;
  opacity: 0.8;
}

/* KOL名称样式 */
.kol-name {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.kol-assigned {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.kol-avatar {
  background: linear-gradient(135deg, #67C23A 0%, #5daf34 100%);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.kol-avatar .el-icon {
  font-size: 12px;
}

.kol-assigned .username {
  color: #67C23A;
  font-weight: 600;
  font-size: 12px;
}

.kol-unassigned {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.unassigned-icon {
  background: #909399;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.unassigned-icon .el-icon {
  font-size: 12px;
}

.kol-unassigned .no-kol {
  color: #909399;
  font-style: italic;
  font-size: 11px;
}

.status-tag:hover::before {
  transform: translateX(100%);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.action-btn .el-icon {
  margin-right: 4px;
}

.action-btn.compact {
  padding: 6px 10px;
  font-size: 12px;
  min-width: auto;
  white-space: nowrap;
}

.action-btn.compact .el-icon {
  margin-right: 3px;
  font-size: 12px;
}

/* 奖励金额样式 */
.reward-amount {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.reward-amount .currency {
  color: #67C23A;
  font-weight: bold;
  font-size: 12px;
}

.reward-amount .amount {
  color: #67C23A;
  font-weight: bold;
  font-size: 14px;
}

.reward-amount.performance .currency,
.reward-amount.performance .amount {
  color: #E6A23C;
}



/* 时间文本样式 */
.time-text {
  color: #ccc;
  font-size: 12px;
}

/* 描述文本样式 */
.description-text {
  color: #ddd;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 链接按钮样式 */
.link-btn {
  color: #409EFF !important;
  font-weight: 500;
}

.link-btn .el-icon {
  margin-right: 4px;
}

/* 无数据样式 */
.no-data {
  color: #666;
  font-style: italic;
}

/* 任务名称状态样式 */
.task-name-status {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.task-name-status .status-icon {
  font-size: 14px;
}

.task-name-status .available {
  color: #67C23A; /* 绿色，表示可用 */
}

.task-name-status .unavailable {
  color: #F56C6C; /* 红色，表示不可用 */
}

/* 任务类型样式 */
.task-type {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-weight: 500;
}

.task-type .type-icon {
  font-size: 14px;
  flex-shrink: 0;
}

/* 奖励类型样式 */
.reward-type-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-weight: 500;
}

.reward-type-display .reward-type-tag {
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  min-width: fit-content;
}

.reward-type-display .reward-type-icon {
  font-size: 14px;
  flex-shrink: 0;
}

/* 货币前缀样式 */
.currency-prefix {
  color: #67C23A !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

/* 表单分段样式 */
.form-section {
  margin-bottom: 24px;
  .section-title {
    font-weight: bold;
  }
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ddd;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-input {
  width: 100%;
}

.form-select {
  width: 100%;
}

.form-date-picker {
  width: 100%;
}

.form-input-number {
  width: 100%;
}

.form-textarea {
  width: 100%;
}

/* 必填字段标识 */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
  font-weight: bold;
}

/* 输入框占位符样式 */
:deep(.create-task-form .el-input__inner::placeholder) {
  color: #999 !important;
}

:deep(.create-task-form .el-textarea__inner::placeholder) {
  color: #999 !important;
}

/* 页面标题样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
}

/* 任务名称状态样式 */
.task-name-status {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.task-name-status .status-icon {
  font-size: 14px;
}

.task-name-status .available {
  color: #67C23A; /* 绿色，表示可用 */
}

.task-name-status .unavailable {
  color: #F56C6C; /* 红色，表示不可用 */
}

/* 日期显示样式 */
.date-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.date-icon {
  font-size: 16px;
  color: #409EFF;
}

.date-icon.end-date {
  color: #E6A23C;
}

/* 日期文本样式 */
.date-text {
  font-size: 12px;
  font-weight: 500;
}

/* 奖励类型选择样式 */
.reward-type-group {
  display: flex;
  flex: 1;
  justify-content: space-between;
}

.reward-radio {
  flex: 1;
  margin-right: 0 !important;
}

.reward-radio :deep(.el-radio__label) {
  width: 100%;
  padding-left: 0;
}

.reward-option {
  display: flex;
  align-items: center;
}


.reward-content {
  display: flex;
}

.reward-title {
  font-size: 13px;
  font-weight: 600;
  padding-left: 5px;
}

.reward-desc {
  color: #999;
  font-size: 11px;
  padding-left: 5px;
}

/* 字段提示样式 */
.field-hint {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.3;
}



/* 转化设置样式 */
.conversion-settings {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 8px;
  border: 1px solid #333;
  padding: 16px;
  margin-top: 12px;
}

.conversion-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.conversion-header-icon {
  color: #E6A23C;
  font-size: 16px;
  background: rgba(230, 162, 60, 0.1);
  padding: 6px;
  border-radius: 6px;
}

.conversion-header-title {
  font-size: 14px;
  font-weight: 600;
}



.conversion-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}



.config-card {
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
  overflow: hidden;
}

.config-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 12px;
  background: linear-gradient(135deg, #2a2a2a 0%, #333 100%);
  border-bottom: 1px solid #444;
}

.config-icon {
  color: #67C23A;
  font-size: 14px;
}

.config-title {
  font-size: 13px;
  font-weight: 500;
}

.config-content {
  padding: 12px;
}



/* 提示图标样式 */
.hint-icon {
  color: #409EFF;
  margin-right: 4px;
  font-size: 12px;
}

/* 字段提示样式增强 */
.field-hint {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.3;
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

/* 素材相关样式 */
.materials-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-btn {
  align-self: flex-start;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.material-item {
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
  overflow: hidden;
  transition: all 0.3s ease;
}

.material-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.material-image {
  position: relative;
}

.preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.material-video {
  position: relative;
}

.video-player {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.material-link {
  padding: 12px;
  display: flex;
}

/* 分页组件样式 - 与页面风格保持一致 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  margin-top: 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-container .el-pagination {
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-color: #dcdfe6;
  --el-pagination-hover-color: #409EFF;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-color: #606266;
}

.pagination-container .el-pagination .el-pager li {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.3s ease;
}

.pagination-container .el-pagination .el-pager li:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

.pagination-container .el-pagination .el-pager li.is-active {
  background: #409EFF;
  border-color: #409EFF;
  color: #ffffff;
}

/* 分页组件按钮样式 */
.pagination-container .el-pagination .btn-prev,
.pagination-container .el-pagination .btn-next {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.pagination-container .el-pagination .btn-prev:hover,
.pagination-container .el-pagination .btn-next:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

/* 分页组件选择器样式 */
.pagination-container .el-pagination .el-select .el-input {
  background: #ffffff;
  border-color: #dcdfe6;
}

.pagination-container .el-pagination .el-select .el-input:hover {
  border-color: #409EFF;
}
</style>
