import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import TaskCenter from '../views/TaskCenter.vue'
import KOLProfile from '../views/KOLProfile.vue'
import Settlement from '../views/Settlement.vue'
import PerformanceCenter from '../views/PerformanceCenter.vue'
// import KOLTaskBoard from '../views/KOLTaskBoard.vue'
import AssetAndSettlement from '../views/AssetAndSettlement.vue'
import UserProfile from '../views/UserProfile.vue'
import APIKeyManage from '../views/APIKeyManage.vue'
import UploadFile from '../views/UploadFile.vue'
import UserVerify from '../views/UserVerify.vue'
// import KOLInvitations from '../views/KOLInvitations.vue'
import KOLTaskDetail from '../views/KOLTaskDetail.vue'
import KOLCenter from '../views/KOLCenter.vue'
import KOLDetail from '../views/KOLDetail.vue'

const routes = [
  { path: '/', name: 'Dashboard', component: Dashboard },
  { path: '/task', name: 'TaskCenter', component: TaskCenter },
  { path: '/kol', name: 'KOLProfile', component: KOLProfile },
  // { path: '/kol-invitations', name: 'KOLInvitations', component: KOLInvitations },
  { path: '/kol-profile', name: 'KOLCenter', component: KOLCenter },
  { path: '/kol/:id/detail', name: 'KOLDetail', component: KOLDetail },
  { path: '/settlement', name: 'Settlement', component: Settlement },
  { path: '/performance', name: 'PerformanceCenter', component: PerformanceCenter },
  { path: '/task-market', name: 'TaskMarket', component: () => import('../views/TaskMarket.vue') },
  { path: '/my-tasks', name: 'KOLTasks', component: () => import('../views/KOLTasks.vue') },
  { path: '/kol-task/:id', name: 'KOLTaskDetail', component: KOLTaskDetail },
  { path: '/asset', name: 'AssetAndSettlement', component: AssetAndSettlement},
  { path: '/profile', name: 'UserProfile', component: UserProfile },
  { path: '/apikey', name: 'APIKeyManage', component: APIKeyManage },
  { path: '/upload', name: 'UploadFile', component: UploadFile },
  { path: '/verify', name: 'UserVerify', component: UserVerify },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
