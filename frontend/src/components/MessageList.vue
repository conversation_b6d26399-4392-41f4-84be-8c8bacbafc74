<template>
  <div>
    <ul>
      <li v-for="msg in messages" :key="msg.id">
            <span :style="{color: msg.is_read ? '#888' : '#409EFF'}">{{ msg.content }}</span>
      <el-button v-if="!msg.is_read == 1" size="mini" @click="markRead(msg.id)">标记已读</el-button>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useMessageStore } from '../store/message'

const messageStore = useMessageStore()
const messages = computed(() => messageStore.messages)
const emit = defineEmits(['read'])

const markRead = async (msgId) => {
  await messageStore.markAsRead(msgId)
  // markAsRead 内部已经调用了 fetchUnreadCount()，不需要再通知父组件
  // emit('read') // 移除重复刷新
}

const formatTime = (t) => new Date(t).toLocaleString()

// 移除 onMounted 中的数据获取，改为在父组件打开弹窗时获取
// onMounted(() => {
//   messageStore.fetchUnreadCount()
// })
</script>