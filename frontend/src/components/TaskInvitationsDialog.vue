<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="任务邀请和申请列表"
    :before-close="handleClose"
  >
    <div class="invitations-applications-container">
      <!-- 邀请列表 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><User /></el-icon>
          <h3 class="section-title">Bit邀请KOL列表</h3>
          <el-tag type="info" size="small">{{ invitations.length }} 个邀请</el-tag>
        </div>

        <div v-if="invitations.length === 0" class="no-data">
          <el-empty description="暂无邀请记录" />
        </div>

        <div v-else class="list-container">
          <el-table :data="invitations" style="width: 100%" class="invitations-table">
            <el-table-column prop="kol_username" label="KOL用户名" width="120" />
            <el-table-column prop="message" label="邀请留言" min-width="200">
              <template #default="scope">
                <span v-if="scope.row.message">{{ scope.row.message }}</span>
                <span v-else class="no-message">无留言</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getInvitationStatusType(scope.row.status)" size="small">
                  {{ getInvitationStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="邀请时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.create_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="response_time" label="响应时间" width="160">
              <template #default="scope">
                <span v-if="scope.row.response_time">{{ formatDateTime(scope.row.response_time) }}</span>
                <span v-else class="no-response">未响应</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 申请列表 -->
      <div class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3 class="section-title">KOL申请列表</h3>
          <el-tag type="warning" size="small">{{ applications.length }} 个申请</el-tag>
        </div>

        <div v-if="applications.length === 0" class="no-data">
          <el-empty description="暂无申请记录" />
        </div>

        <div v-else class="list-container">
          <el-table :data="applications" style="width: 100%" class="applications-table">
            <el-table-column prop="kol_username" label="KOL用户名" width="150" />
            <el-table-column prop="application_reason" label="申请理由" min-width="200">
              <template #default="scope">
                <span v-if="scope.row.application_reason">{{ scope.row.application_reason }}</span>
                <span v-else class="no-reason">无申请理由</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="申请状态" width="120" align="center">
              <template #default="scope">
                <el-tag :type="getApplicationStatusType(scope.row.status)" size="small" effect="dark">
                  {{ getApplicationStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="申请时间" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.create_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="response_time" label="处理时间" width="160">
              <template #default="scope">
                <span v-if="scope.row.response_time">{{ formatDateTime(scope.row.response_time) }}</span>
                <span v-else class="no-response">未处理</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center" v-if="showActions">
              <template #default="scope">
                <div v-if="scope.row.status === 'pending'" class="action-buttons">
                  <el-button type="success" size="small" @click="approveApplication(scope.row)">
                    批准
                  </el-button>
                  <el-button type="danger" size="small" @click="rejectApplication(scope.row)">
                    拒绝
                  </el-button>
                </div>
                <span v-else class="no-action">已处理</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-drawer>

  <!-- 渠道码输入对话框 -->
  <el-dialog
    v-model="channelCodeVisible"
    title="输入渠道码"
    width="500px"
    :before-close="handleChannelCodeClose"
  >
    <el-form :model="channelCodeForm" :rules="channelCodeRules" ref="channelCodeFormRef" label-width="80px">
      <el-form-item label="渠道码" prop="channel_code" required>
        <el-input
          v-model="channelCodeForm.channel_code"
          placeholder="请输入渠道码"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="channelCodeForm.remark"
          type="textarea"
          placeholder="可选: 添加渠道码相关备注"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleChannelCodeClose">取消</el-button>
        <el-button type="primary" @click="handleChannelCodeConfirm" :loading="channelCodeLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Document } from '@element-plus/icons-vue'
import apiService from '@/utils/api'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: [Number, String],
    required: true
  },
  showActions: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close'])

const invitations = ref([])
const applications = ref([])
const loading = ref(false)

// 渠道码相关
const channelCodeVisible = ref(false)
const channelCodeLoading = ref(false)
const channelCodeFormRef = ref(null)
const pendingApplication = ref(null)

const channelCodeForm = ref({
  channel_code: '',
  remark: ''
})

const channelCodeRules = {
  channel_code: [
    { required: true, message: '请输入渠道码', trigger: 'blur' },
    { min: 1, max: 50, message: '渠道码长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 200, message: '备注长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 获取邀请和申请列表
const fetchInvitationsAndApplications = async () => {
  if (!props.taskId) return

  loading.value = true
  try {
    const response = await apiService.getTaskInvitationsAndApplications(props.taskId)
    invitations.value = response.data.invitations || []
    applications.value = response.data.applications || []
    
    // 添加调试信息
    console.log('=== 申请列表数据调试 ===')
    console.log('申请数量:', applications.value.length)
    applications.value.forEach((app, index) => {
      console.log(`申请 ${index + 1}:`, {
        id: app.id,
        kol_username: app.kol_username,
        status: app.status,
        application_reason: app.application_reason,
        create_time: app.create_time
      })
    })
    console.log('=== 调试结束 ===')
  } catch (error) {
    console.error('获取邀请和申请列表失败:', error)
    ElMessage.error('获取邀请和申请列表失败')
  } finally {
    loading.value = false
  }
}

// 批准申请
const approveApplication = async (application) => {
  try {
    await ElMessageBox.confirm(
      `确定要批准 ${application.kol_username} 的申请吗？`,
      '确认批准',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 保存待处理的申请
    pendingApplication.value = application
    
    // 弹出渠道码输入对话框
    channelCodeVisible.value = true
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批准申请失败:', error)
      ElMessage.error('批准申请失败')
    }
  }
}

// 拒绝申请
const rejectApplication = async (application) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝原因',
      '拒绝申请',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入拒绝原因'
      }
    )

    if (reason) {
      // 调用拒绝申请的API
      await apiService.rejectApplication({
        task_id: props.taskId,
        kol_id: application.kol_id,
        application_id: application.id,
        reject_reason: reason
      })
      
      // 发送消息通知给KOL
      try {
        const notificationContent = `❌ 很抱歉，您的任务申请已被拒绝。\n\n任务ID: ${props.taskId}\n拒绝原因: ${reason}\n\n您可以查看其他适合的任务，或联系项目方了解更多信息。`
        
        await apiService.sendMessage({
          user_id: application.kol_id,
          content: notificationContent,
          message_type: 'task'
        })
        
        console.log('拒绝通知发送成功')
      } catch (messageError) {
        console.error('发送拒绝通知失败:', messageError)
        // 消息发送失败不影响主要操作
      }

      ElMessage.success('申请已拒绝，已发送通知给KOL')
      // 重新获取数据
      await fetchInvitationsAndApplications()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝申请失败:', error)
      ElMessage.error('拒绝申请失败')
    }
  }
}

// 邀请状态标签映射
const getInvitationStatusLabel = (status) => {
  const statusMap = {
    'pending': '待响应',
    'accepted': '已接受',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 邀请状态类型映射
const getInvitationStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'accepted': 'success',
    'rejected': 'danger',
    'expired': 'info'
  }
  return typeMap[status] || 'info'
}

// 申请状态标签映射
const getApplicationStatusLabel = (status) => {
  const statusMap = {
    'pending': '待处理',
    'accepted': '已批准',
    'rejected': '已拒绝',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 申请状态类型映射
const getApplicationStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'accepted': 'success',
    'rejected': 'danger',
    'expired': 'info'
  }
  return typeMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 监听visible变化，当显示时获取数据
watch(() => props.visible, (newVal) => {
  if (newVal && props.taskId) {
    fetchInvitationsAndApplications()
  }
})

// 渠道码对话框关闭
const handleChannelCodeClose = () => {
  channelCodeVisible.value = false
  channelCodeForm.value = {
    channel_code: '',
    remark: ''
  }
  pendingApplication.value = null
}

// 渠道码确认
const handleChannelCodeConfirm = async () => {
  try {
    // 验证表单
    await channelCodeFormRef.value.validate()
    
    channelCodeLoading.value = true
    
    // 先更新渠道码
    await apiService.updateTaskChannelCode({
      task_id: props.taskId,
      channel_code: channelCodeForm.value.channel_code,
      remark: channelCodeForm.value.remark
    })
    
    // 然后批准申请
    if (pendingApplication.value) {
      await apiService.approveApplication({
        task_id: props.taskId,
        kol_id: pendingApplication.value.kol_id,
        application_id: pendingApplication.value.id
      })
      
      // 发送消息通知给KOL
      try {
        const notificationContent = `🎉 恭喜！您的任务申请已被批准！\n\n任务ID: ${props.taskId}\n渠道码: ${channelCodeForm.value.channel_code}\n\n任务已分配给您，请及时查看任务详情并开始执行。`
        
        await apiService.sendMessage({
          user_id: pendingApplication.value.kol_id,
          content: notificationContent,
          message_type: 'task'
        })
        
        console.log('消息通知发送成功')
      } catch (messageError) {
        console.error('发送消息通知失败:', messageError)
        // 消息发送失败不影响主要操作
      }
    }
    
    ElMessage.success('渠道码设置成功，申请已批准，已发送通知给KOL')
    
    // 关闭对话框
    handleChannelCodeClose()
    
    // 重新获取数据
    await fetchInvitationsAndApplications()
    
  } catch (error) {
    console.error('设置渠道码或批准申请失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    channelCodeLoading.value = false
  }
}
</script>

<style scoped>
.invitations-applications-container {
}

.section {
  margin-bottom: 32px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.section-icon {
  color: #409EFF;
  font-size: 18px;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
  flex: 1;
}

.no-data {
  padding: 40px;
  text-align: center;
}

.list-container {
  margin-top: 16px;
}

.no-message,
.no-reason,
.no-response,
.no-action {
  color: #666;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 渠道码对话框样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #eee;
  padding: 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #eee;
  padding: 15px 20px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 状态标签样式优化 */
.applications-table .el-tag {
  font-weight: 500;
  border-radius: 4px;
}

.applications-table .el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

.applications-table .el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.applications-table .el-tag--danger {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.applications-table .el-tag--info {
  background-color: #f4f4f5;
  border-color: #909399;
  color: #909399;
}
</style>
