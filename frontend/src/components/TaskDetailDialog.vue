<template>
  <div class="task-detail-dialog">


    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else>
      <!-- 第一部分：任务基本信息 -->
      <div class="detail-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h3 class="section-title">任务基本信息</h3>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">任务名称:</span>
            <span class="value">{{ taskDetail?.task_name || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务类型:</span>
            <span class="value">{{ getTaskTypeLabel(taskDetail?.task_type) }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务状态:</span>
            <el-tag :type="getStatusType(taskDetail?.task_status)" size="small">
              {{ getStatusLabel(taskDetail?.task_status) }}
            </el-tag>
          </div>



          <!-- KOL邀请与申请列表区域 -->
          <div class="info-item full-width" style="margin-top: 16px;">
            <span class="label">查看KOL邀请与申请列表:</span>
            <div class="status-actions">
              <el-button type="primary" @click="showInvitationsAndApplications" class="action-btn">
                <el-icon><User /></el-icon>
                &nbsp;查看邀请和申请
              </el-button>
            </div>
          </div>

          <!-- 可执行操作按钮 -->
          <div class="info-item full-width" v-if="availableStatuses.length > 0 && taskDetail?.task_status !== 'completed' && taskDetail?.task_status !== 'cancelled'">
            <span class="label">可执行操作:</span>
            <div class="executable-actions">
              <!-- 审核操作按钮 - 只在特定条件下显示 -->
              <div v-if="shouldShowReviewButtons()" class="status-action-item">
                <div class="review-buttons">
                  <el-button
                    type="success"
                    @click="changeTaskStatus('approved')"
                    :loading="statusLoading"
                    size="default"
                    class="status-action-btn"
                  >
                    审核通过
                  </el-button>
                  <el-button
                    type="danger"
                    @click="showRejectDialog"
                    :loading="statusLoading"
                    size="default"
                    class="status-action-btn"
                  >
                    审核拒绝
                  </el-button>
                </div>
                <div class="status-description">
                  {{ getReviewDescription() }}
                </div>
              </div>
              
              <!-- 其他状态显示单个按钮 - 排除审核相关状态 -->
              <div v-for="status in getNonReviewStatuses()" :key="status" class="status-action-item">
                <el-tooltip
                  v-if="status === 'assigned' && !hasInvitationsOrApplications"
                  content="没有邀请KOL或者没有KOL申请该任务"
                  placement="top"
                  effect="dark"
                >
                  <el-button
                    :type="getStatusButtonType(status)"
                    @click="changeTaskStatus(status)"
                    :loading="statusLoading"
                    :disabled="status === 'assigned' && !hasInvitationsOrApplications"
                    size="default"
                    class="status-action-btn"
                  >
                    {{ getStatusDisplayName(status) }}
                  </el-button>
                </el-tooltip>
                <el-button
                  v-else
                  :type="getStatusButtonType(status)"
                  @click="changeTaskStatus(status)"
                  :loading="statusLoading"
                  size="default"
                  class="status-action-btn"
                >
                  {{ getStatusDisplayName(status) }}
                </el-button>
                <div class="status-description">
                  {{ getStatusDescription(status) }}
                </div>
              </div>
            </div>
          </div>

          <!-- KOL提交的资料 - 非草稿状态才显示 -->
          <div class="info-item full-width" v-if="taskDetail?.published_links && taskDetail?.task_status !== 'draft'">
            <span class="label">KOL提交的资料:</span>
            <div class="kol-submitted-content">
              <div class="submitted-header">
                <span class="submitted-title">🔗 {{ getPlatformLabel(getPublishedPlatform(taskDetail.published_links)) }}</span>
                <span class="submit-time">📅 提交时间：{{ getPublishedTime(taskDetail.published_links) }}</span>
              </div>
              <div class="submitted-links-box">
                <div v-for="(link, index) in getPublishedLinks(taskDetail.published_links)" :key="index" class="submitted-link-item">
                  <span class="link-icon">{{ getPlatformIcon(getPublishedPlatform(taskDetail.published_links)) }}</span>
                  <a :href="link" target="_blank" class="link-url">{{ link }}</a>
                </div>
              </div>
              <div v-if="getPublishedNotes(taskDetail.published_links)" class="submitted-notes">
                <div class="notes-title">💡 提交说明:</div>
                <div class="notes-content">{{ getPublishedNotes(taskDetail.published_links) }}</div>
              </div>
            </div>
          </div>
          <div class="info-item">
            <span class="label">开始日期:</span>
            <span class="value">{{ taskDetail?.start_date || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">结束日期:</span>
            <span class="value">{{ taskDetail?.end_date || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">奖励类型:</span>
            <span class="value">{{ getRewardTypeLabel(taskDetail?.reward_type) }}</span>
          </div>

          <!-- 品牌推广模式显示基础奖励 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding'">
            <span class="label">基础奖励:</span>
            <span class="value reward">${{ taskDetail?.base_reward || 0 }}</span>
          </div>

          <!-- 带单返佣模式显示返佣比例 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'commission'">
            <span class="label">返佣比例:</span>
            <span class="value reward">{{ taskDetail?.commission_rate || 0 }}%</span>
          </div>

          <!-- 品牌推广+按转化付费模式显示基础奖励和每FTT奖励 -->
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding_plus_conversion'">
            <span class="label">基础奖励:</span>
            <span class="value reward">${{ taskDetail?.base_reward || 0 }}</span>
          </div>
          <div class="info-item" v-if="taskDetail?.reward_type === 'branding_plus_conversion'">
            <span class="label">每FTT奖励:</span>
            <span class="value reward">${{ taskDetail?.performance_rate || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">渠道码:</span>
            <span class="value">{{ taskDetail?.channel_code || '-' }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">任务描述:</span>
            <div class="value description">{{ taskDetail?.description || '-' }}</div>
          </div>

          <!-- 草稿内容展示 -->
          <div class="info-item full-width" v-if="shouldShowDraftContent(taskDetail)">
            <span class="label">草稿内容:</span>
            <div class="draft-content-section">
              <!-- 草稿内容 -->
              <div class="draft-text-box">
                {{ getDraftContentPreview(taskDetail.draft_content, 300) }}
              </div>

              <!-- 创作说明 -->
              <div v-if="getDraftCreationNotes(taskDetail.draft_content)" class="creation-notes-box">
                <div class="notes-title">💡 创作说明:</div>
                <div class="notes-content">{{ getDraftCreationNotes(taskDetail.draft_content) }}</div>
              </div>

              <!-- 素材展示 -->
              <div v-if="getDraftMaterials(taskDetail.draft_content).length > 0" class="materials-box">
                <div class="materials-title">📎 创作素材:</div>
                <div class="materials-preview">
                  <div v-for="(material, index) in getDraftMaterials(taskDetail.draft_content)" :key="index" class="material-item">

                    <!-- 图片预览 -->
                    <div v-if="material.type === 'image'" class="material-image">
                      <el-image
                        :src="material.url"
                        :preview-src-list="[material.url]"
                        fit="cover"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <div>图片加载失败</div>
                          </div>
                        </template>
                      </el-image>
                    </div>

                    <!-- 视频预览 -->
                    <div v-else-if="material.type === 'video'" class="material-video">
                      <video
                        :src="material.url"
                        controls
                        preload="metadata"
                        class="video-player"
                      ></video>
                    </div>

                    <!-- 链接预览 -->
                    <div v-else class="material-link">
                      <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                        <span class="material-icon">{{ getMaterialIcon(material.type) }}</span>
                        {{ material.name || material.url }}
                      </el-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="info-item full-width" v-if="taskDetail?.official_materials">
            <span class="label">官方素材:</span>
            <div class="materials-preview">
              <div v-for="(material, index) in taskDetail.official_materials" :key="index" class="material-item">
                <!-- 图片预览 -->
                <div v-if="material.type === 'image'" class="material-image">
                  <el-image
                    :src="material.url"
                    :preview-src-list="[material.url]"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>

                <!-- 视频预览 -->
                <div v-else-if="material.type === 'video'" class="material-video">
                  <video
                    :src="material.url"
                    controls
                    preload="metadata"
                    class="video-player"
                  ></video>
                </div>

                <!-- 链接预览 -->
                <div v-else class="material-link">
                  <el-link :href="ensureFullUrl(material.url)" target="_blank" type="primary">
                    {{ material.name || material.url }}
                  </el-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二部分：统计信息 (data_source为api) -->
      <div class="detail-section"  v-if="taskDetail?.task_status !== 'draft'">
        <div class="section-header">
                     <el-icon class="section-icon"><DataLine /></el-icon>
           <h3 class="section-title">统计信息</h3>
         </div>
         <div v-if="apiStats.length === 0" class="no-data">
           <el-empty description="暂无统计数据" />
         </div>
         <div v-else class="stats-grid">
           <div class="stats-card card1">
             <div class="stats-label">总点击量</div>
             <div class="stats-value click">{{ totalApiStats.click_count }}</div>
           </div>
           <div class="stats-card card2">
             <div class="stats-label">总注册量</div>
             <div class="stats-value register">{{ totalApiStats.register_count }}</div>
           </div>
           <div class="stats-card card3">
             <div class="stats-label">总FTT值</div>
             <div class="stats-value ftt">${{ totalApiStats.ftt }}</div>
           </div>
           <div class="stats-card card4">
             <div class="stats-label">总入金金额</div>
             <div class="stats-value deposit">${{ totalApiStats.deposit_amount }}</div>
           </div>
         </div>

         <!-- 详细数据表格 -->
         <div v-if="apiStats.length > 0" class="stats-table">
           <h4>详细数据</h4>
           <el-table :data="apiStats" class="stats-data-table" border>
             <el-table-column prop="stat_date" label="日期" />
             <el-table-column prop="daily_clicks" label="点击量" />
             <el-table-column prop="daily_registers" label="注册量" />
             <el-table-column prop="daily_ftt" label="FTT值">
               <template #default="scope">
                 ${{ scope.row.daily_ftt }}
               </template>
             </el-table-column>
             <el-table-column prop="daily_deposit" label="入金金额" width="120">
               <template #default="scope">
                 ${{ scope.row.daily_deposit }}
               </template>
             </el-table-column>
           </el-table>
         </div>
       </div>

       <!-- 第三部分：渠道统计信息 (按channel_code查询) -->
       <div class="detail-section"  v-if="taskDetail?.task_status !== 'draft'">
         <div class="section-header">
           <el-icon class="section-icon"><Link /></el-icon>
          <h3 class="section-title">渠道统计信息</h3>
        </div>
        <div v-if="channelStats.length === 0" class="no-data">
          <el-empty description="暂无渠道数据" />
        </div>
        <div v-else class="channel-stats">
          <div class="channel-summary">
            <h4>渠道汇总 ({{ taskDetail?.channel_code || '-' }})</h4>
            <div class="stats-grid">
              <div class="stats-card">
                <div class="stats-label">渠道总点击</div>
                <div class="stats-value click">{{ totalChannelStats.click_count }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总注册</div>
                <div class="stats-value register">{{ totalChannelStats.register_count }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总FTT</div>
                <div class="stats-value ftt">${{ totalChannelStats.ftt }}</div>
              </div>
              <div class="stats-card">
                <div class="stats-label">渠道总入金</div>
                <div class="stats-value deposit">${{ totalChannelStats.deposit_amount }}</div>
              </div>
            </div>
          </div>

          <!-- 渠道详细数据表格 -->
          <div class="stats-table">
            <h4>渠道详细数据</h4>
            <el-table :data="channelStats" style="width: 100%" class="stats-data-table">
              <el-table-column prop="stat_date" label="日期" width="120" />
              <el-table-column prop="daily_clicks" label="点击量" width="100" />
              <el-table-column prop="daily_registers" label="注册量" width="100" />
              <el-table-column prop="daily_ftt" label="FTT值" width="120">
                <template #default="scope">
                  ${{ scope.row.daily_ftt }}
                </template>
              </el-table-column>
              <el-table-column prop="daily_deposit" label="入金金额" width="120">
                <template #default="scope">
                  ${{ scope.row.daily_deposit }}
                </template>
              </el-table-column>
              <el-table-column prop="data_source" label="数据来源" width="100" />
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态流转对话框 -->
    <el-drawer v-model="statusFlowVisible" title="任务状态管理" size="40%" :before-close="() => statusFlowVisible = false">
      <TaskStatusFlow v-if="taskDetail" :task-id="taskDetail.id" :current-status="taskDetail.task_status"
        @status-changed="handleStatusChanged" />
    </el-drawer>

    <!-- 在组件底部添加PublishLinksDialog -->
    <el-dialog
      v-model="publishLinksVisible"
      title="KOL提交链接"
      width="600px"
      :before-close="() => publishLinksVisible = false"
    >
      <PublishLinksDialog :links="taskDetail?.published_links || []" />
    </el-dialog>

    <!-- 邀请和申请对话框 -->
    <TaskInvitationsDialog
      v-model:visible="invitationsAndApplicationsVisible"
      :task-id="taskDetail?.id"
      :show-actions="true"
      @close="() => invitationsAndApplicationsVisible = false"
    />

    <!-- 审核拒绝对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核拒绝"
      width="500px"
      :before-close="() => rejectDialogVisible = false"
    >
      <div class="reject-dialog-content">
        <p class="reject-tip">请输入拒绝理由：</p>
        <el-input
          v-model="rejectReason"
          type="textarea"
          :rows="4"
          placeholder="请输入详细的拒绝理由..."
          maxlength="500"
          show-word-limit
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="statusLoading">
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watchEffect } from 'vue'
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus'
import { Document, DataLine, Link, Setting, Close, Picture, User } from '@element-plus/icons-vue'
import apiService from '@/utils/api'
import TaskStatusFlow from './TaskStatusFlow.vue'
import PublishLinksDialog from './PublishLinksDialog.vue'
import TaskInvitationsDialog from './TaskInvitationsDialog.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

// 立即执行的调试信息
console.log('🎯 TaskDetailDialog script setup executed!')
console.log('Props in script setup:', props)

const emit = defineEmits(['close', 'status-changed'])

const loading = ref(false)
const taskDetail = ref(null)
const apiStats = ref([])
const channelStats = ref([])

// 状态管理相关状态
const statusFlowVisible = ref(false)

// 添加新的响应式变量
const publishLinksVisible = ref(false)
const invitationsAndApplicationsVisible = ref(false)

// 可执行操作相关状态
const availableStatuses = ref([])
const statusLoading = ref(false)
const hasInvitationsOrApplications = ref(false)
const rejectDialogVisible = ref(false)
const rejectReason = ref('')

// 计算总计数据（API数据）
const totalApiStats = computed(() => {
  if (!Array.isArray(apiStats.value) || apiStats.value.length === 0) {
    return { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 }
  }

  return apiStats.value.reduce((total, item) => {
    return {
      click_count: total.click_count + (item.daily_clicks || 0),
      register_count: total.register_count + (item.daily_registers || 0),
      ftt: total.ftt + (parseFloat(item.daily_ftt) || 0),
      deposit_amount: total.deposit_amount + (parseFloat(item.daily_deposit) || 0)
    }
  }, { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 })
})

// 计算总计数据（渠道数据）
const totalChannelStats = computed(() => {
  if (!Array.isArray(channelStats.value) || channelStats.value.length === 0) {
    return { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 }
  }

  return channelStats.value.reduce((total, item) => {
    return {
      click_count: total.click_count + (item.daily_clicks || 0),
      register_count: total.register_count + (item.daily_registers || 0),
      ftt: total.ftt + (parseFloat(item.daily_ftt) || 0),
      deposit_amount: total.deposit_amount + (parseFloat(item.daily_deposit) || 0)
    }
  }, { click_count: 0, register_count: 0, ftt: 0, deposit_amount: 0 })
})

// 获取任务详情数据
const fetchTaskDetail = async () => {
  loading.value = true
  try {
    console.log('=== fetchTaskDetail Debug ===')
    console.log('Props task:', props.task)
    console.log('Task ID:', props.task.id)
    console.log('Task name:', props.task.task_name)
    console.log('Reward type:', props.task.reward_type)
    console.log('Task ID type:', typeof props.task.id)
    console.log('Task ID value:', props.task.id)

    // 验证taskId是否有效
    if (!props.task.id || props.task.id === undefined || props.task.id === null) {
      console.error('❌ Task ID is invalid:', props.task.id)
      throw new Error('Invalid task ID')
    }

    console.log('✅ Task ID is valid, calling API...')

    // 从API获取完整的任务详情
    const taskDetailResponse = await apiService.getTaskDetail(props.task.id)
    console.log('Task Detail Response:', taskDetailResponse)

    // 确保我们使用response.data
    const taskData = taskDetailResponse.data
    console.log('Task Data before processing:', taskData)
    console.log('Published Links before processing:', taskData.published_links)

    // 处理published_links字段，确保它是一个数组
    if (taskData.published_links) {
      try {
        taskData.published_links = typeof taskData.published_links === 'string'
          ? JSON.parse(taskData.published_links)
          : taskData.published_links
        console.log('Published Links after parsing:', taskData.published_links)
      } catch (e) {
        console.error('解析published_links失败:', e)
        taskData.published_links = []
      }
    } else {
      taskData.published_links = []
    }

    // 处理official_materials字段，确保它是一个数组
    if (taskData.official_materials) {
      try {
        taskData.official_materials = typeof taskData.official_materials === 'string'
          ? JSON.parse(taskData.official_materials)
          : taskData.official_materials
        console.log('Official Materials after parsing:', taskData.official_materials)
      } catch (e) {
        console.error('解析official_materials失败:', e)
        taskData.official_materials = []
      }
    } else {
      taskData.official_materials = []
    }

    taskDetail.value = taskData
    console.log('Final task detail:', taskDetail.value)

    console.log('Task detail after API call:', taskDetail.value)
    console.log('=== End fetchTaskDetail Debug ===')

    // 获取API统计数据 (data_source为api)
    const apiStatsResponse = await apiService.getTaskApiStats(props.task.id)
    console.log('API Stats Response:', apiStatsResponse.data)
    // 后端返回的是包含daily_stats的对象，需要提取数组
    apiStats.value = apiStatsResponse.data?.daily_stats || []
    console.log('Processed API Stats:', apiStats.value)

    // 获取渠道统计数据 (按channel_code查询)
    if (taskDetail.value.channel_code) {
      const channelStatsResponse = await apiService.getTaskChannelStats(props.task.id, {
        params: { channel_code: taskDetail.value.channel_code }
      })
      console.log('Channel Stats Response:', channelStatsResponse.data)
      // 后端返回的是包含channel_data的对象，需要提取对应渠道的数据
      const channelData = channelStatsResponse.data?.channel_data || {}
      channelStats.value = channelData[taskDetail.value.channel_code] || []
      console.log('Processed Channel Stats:', channelStats.value)
    }

  } catch (error) {
    console.error('获取任务详情失败:', error)
    // ElMessage.error('获取任务详情失败')
    // 使用传入的基本数据作为备用
    taskDetail.value = { ...props.task }
    apiStats.value = []
    channelStats.value = []
  } finally {
    loading.value = false
  }
  
  // 获取可用状态列表
  if (props.task?.id) {
    try {
      await fetchAvailableStatuses()
      await checkInvitationsAndApplications()
    } catch (error) {
      console.error('获取可用状态失败:', error)
    }
  }
}

// 任务类型标签映射
const getTaskTypeLabel = (type) => {
  const typeMap = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA活动'
  }
  return typeMap[type] || type || '-'
}

// 状态标签映射
const getStatusLabel = (status) => {
  const statusMap = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '已分配',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝',
    'running': '运行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status || '-'
}

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'published': 'warning',
    'assigned': 'primary',
    'unapproved': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'running': 'success',
    'completed': 'success',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// 奖励类型标签映射
const getRewardTypeLabel = (type) => {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌推广+按转化付费'
  }
  return typeMap[type] || type || '-'
}

// URL处理函数
const ensureFullUrl = (url) => {
  if (!url) return url
  // 如果URL不是以http://或https://开头，则添加https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }
  return url
}

// 状态管理相关函数
const showStatusFlow = () => {
  console.log('=== showStatusFlow Debug ===')
  console.log('Task detail:', taskDetail.value)
  console.log('Task ID:', taskDetail.value?.id)
  console.log('Task status:', taskDetail.value?.task_status)

  statusFlowVisible.value = true
  console.log('=== End showStatusFlow Debug ===')
}

// 取消任务
const cancelTask = async () => {
  try {
    console.log('=== 取消任务调试信息 ===')
    console.log('任务信息:', taskDetail.value)

    await ElMessageBox.confirm(
      '确定要取消这个任务吗？此操作不可恢复。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await apiService.changeTaskStatus({
      task_id: taskDetail.value.id,
      new_status: 'cancelled',
      type: 1 // 默认为商户类型
    })

    console.log('取消任务响应:', response)
    ElMessage.success(response.data.message || '任务已取消')

    // 更新本地任务状态
    if (taskDetail.value) {
      taskDetail.value.task_status = 'cancelled'
    }

    // 通知父组件状态已更改
    emit('status-changed', {
      taskId: taskDetail.value.id,
      oldStatus: 'previous',
      newStatus: 'cancelled'
    })

  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error(error.response?.data?.detail || '取消任务失败')
    }
  }
}

// 状态变更回调
const handleStatusChanged = ({ taskId, oldStatus, newStatus }) => {
  console.log('Status changed:', { taskId, oldStatus, newStatus })

  // 更新本地任务状态
  if (taskDetail.value && taskDetail.value.id === taskId) {
    taskDetail.value.task_status = newStatus
    console.log('Updated task status to:', newStatus)
  }

  // 关闭状态流转对话框
  statusFlowVisible.value = false

  // 通知父组件状态已更改
  emit('status-changed', { taskId, oldStatus, newStatus })
}

// 添加新的方法
const showPublishLinks = () => {
  publishLinksVisible.value = true
}

const showInvitationsAndApplications = () => {
  invitationsAndApplicationsVisible.value = true
}

// 草稿内容相关辅助函数
function shouldShowDraftContent(task) {
  const validStatuses = ['unapproved', 'approved', 'running', 'completed']
  return validStatuses.includes(task?.task_status) && task?.draft_content
}

function getDraftContentPreview(draftContent, maxLength = 100) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.content) {
      return truncateText(draftData.content, maxLength)
    }
  } catch (error) {
    // 解析失败，当作纯文本处理
  }

  return truncateText(draftContent, maxLength)
}

function getDraftCreationNotes(draftContent) {
  if (!draftContent) return ''

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.creation_notes) {
      return draftData.creation_notes
    }
  } catch (error) {
    // 解析失败，返回空
  }

  return ''
}

function getDraftMaterials(draftContent) {
  if (!draftContent) return []

  try {
    const draftData = JSON.parse(draftContent)
    if (typeof draftData === 'object' && draftData.materials) {
      return draftData.materials || []
    }
  } catch (error) {
    console.error('解析草稿素材失败:', error)
  }

  return []
}

function getMaterialIcon(materialType) {
  const iconMap = {
    'image': '🖼️',
    'video': '📹',
    'link': '🔗',
    'file': '📄'
  }
  return iconMap[materialType] || '📎'
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}



function getPublishedLinks(publishedLinksData) {
  if (!publishedLinksData) return []

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    return publishedData.links || []
  } catch (error) {
    console.warn('解析 published_links 失败:', error)
    return []
  }
}

function getPublishedPlatform(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    return publishedData.platform || ''
  } catch (error) {
    return ''
  }
}

function getPublishedTime(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    const publishTime = publishedData.publish_time || publishedData.submit_time
    
    if (!publishTime) {
      return '未知时间'
    }
    
    // 尝试解析ISO格式的日期字符串
    const date = new Date(publishTime)
    if (isNaN(date.getTime())) {
      console.warn('Invalid date in published_links:', publishTime)
      return '未知时间'
    }
    
    return formatDateTime(publishTime)
  } catch (error) {
    console.warn('Error parsing published_links time:', error)
    return '未知时间'
  }
}

function getPublishedNotes(publishedLinksData) {
  if (!publishedLinksData) return ''

  try {
    const publishedData = typeof publishedLinksData === 'string'
      ? JSON.parse(publishedLinksData)
      : publishedLinksData

    return publishedData.notes || ''
  } catch (error) {
    return ''
  }
}

function getPlatformIcon(platform) {
  const iconMap = {
    'twitter': '🐦',
    'youtube': '📹',
    'medium': '📝',
    'instagram': '📷',
    'other': '🔗'
  }
  return iconMap[platform?.toLowerCase()] || '🔗'
}

function getPlatformLabel(platform) {
  const labelMap = {
    'twitter': 'Twitter/X',
    'youtube': 'YouTube',
    'medium': 'Medium',
    'instagram': 'Instagram',
    'other': '其他平台'
  }
  return labelMap[platform?.toLowerCase()] || '未知平台'
}

// 在现有的辅助函数后面添加formatDateTime函数
function formatDateTime(dateStr) {
  if (!dateStr) return ''
  
  const date = new Date(dateStr)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date string:', dateStr)
    return '未知时间'
  }
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听props变化，确保组件挂载时执行
watchEffect(() => {
  if (props.task) {
    console.log('🎯 TaskDetailDialog component mounted or props changed!')
    console.log('Props received:', props)
    console.log('Task object:', props.task)
    fetchTaskDetail()
  }
})

// 保留onMounted作为备用
onMounted(() => {
  console.log('🎯 TaskDetailDialog onMounted called!')
  if (props.task) {
    console.log('Task exists in onMounted, calling fetchTaskDetail')
    fetchTaskDetail()
  } else {
    console.log('No task in onMounted, waiting for props...')
  }
})

// 状态管理相关函数
const fetchAvailableStatuses = async () => {
  try {
    console.log('Fetching available statuses for task:', props.task.id)
    const response = await apiService.getBitTaskStatusFlow(props.task.id)
    console.log('Status flow response:', response.data)
    
    availableStatuses.value = response.data.available_statuses || []
    console.log('Available statuses:', availableStatuses.value)
  } catch (error) {
    console.error('获取可用状态失败:', error)
    availableStatuses.value = []
  }
}

// 检查是否有邀请或申请
const checkInvitationsAndApplications = async () => {
  try {
    if (!props.task?.id) return
    
    const response = await apiService.getTaskInvitationsAndApplications(props.task.id)
    console.log('Invitations and applications response:', response.data)
    
    // 检查是否有邀请或申请数据
    const hasData = response.data && (
      (response.data.invitations && response.data.invitations.length > 0) ||
      (response.data.applications && response.data.applications.length > 0)
    )
    
    hasInvitationsOrApplications.value = hasData
    console.log('Has invitations or applications:', hasInvitationsOrApplications.value)
    console.log('Available statuses:', availableStatuses.value)
    console.log('Is assigned status available:', availableStatuses.value.includes('assigned'))
    console.log('Should show tooltip for assigned:', availableStatuses.value.includes('assigned') && !hasInvitationsOrApplications.value)
  } catch (error) {
    console.error('检查邀请和申请失败:', error)
    hasInvitationsOrApplications.value = false
  }
}

const getStatusDisplayName = (status) => {
  const statusNames = {
    'draft': '草稿',
    'published': '发布',
    'assigned': '分配',
    'unapproved': '审核通过',
    'approved': '审核通过',
    'rejected': '审核拒绝待修改',
    'running': '运行中',
    'completed': '完成',
    'cancelled': '取消'
  }
  return statusNames[status] || status
}

const getStatusButtonType = (status) => {
  const buttonTypes = {
    'draft': 'primary',
    'published': 'success',
    'assigned': 'primary',
    'unapproved': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'running': 'success',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return buttonTypes[status] || 'default'
}

const getStatusDescription = (status) => {
  const descriptions = {
    'draft': '任务创建后的初始状态，可以修改任务内容和要求',
    'published': '任务已发布到需求池，等待KOL申请',
    'assigned': 'KOL已接受任务，可以提交资料进行审核',
    'unapproved': 'KOL已提交内容，等待品牌方审核',
    'approved': '内容审核通过，任务进入运行中',
    'rejected': '内容需要修改，KOL需要重新制作',
    'running': '任务开始运行，KOL可以开始发布内容',
    'completed': '任务已完成，所有工作都已结束',
    'cancelled': '任务已被取消，不再继续执行'
  }
  return descriptions[status] || '未知状态'
}

// 判断是否为审核相关状态
const isReviewStatus = (status) => {
  return status === 'approved' || status === 'rejected'
}

// 判断是否应该显示审核按钮
const shouldShowReviewButtons = () => {
  const currentStatus = taskDetail.value?.task_status
  const hasPublishedLinks = taskDetail.value?.published_links
  
  // 确保availableStatuses.value存在
  if (!availableStatuses.value || !Array.isArray(availableStatuses.value)) {
    return false
  }
  
  // 当任务状态为已分配且有KOL提交的资料时，显示审核按钮
  if (currentStatus === 'assigned' && hasPublishedLinks && (availableStatuses.value.includes('approved') || availableStatuses.value.includes('rejected'))) {
    return true
  }
  
  // 当任务状态为待审核时，显示审核按钮
  if (currentStatus === 'unapproved' && (availableStatuses.value.includes('approved') || availableStatuses.value.includes('rejected'))) {
    return true
  }
  
  // 当任务状态为审核通过时，不显示审核按钮
  if (currentStatus === 'approved') {
    return false
  }
  
  return false
}

// 获取审核按钮的描述
const getReviewDescription = () => {
  const currentStatus = taskDetail.value?.task_status
  
  if (currentStatus === 'assigned') {
    return 'KOL已提交资料，请进行审核'
  } else if (currentStatus === 'unapproved') {
    return 'KOL已提交内容，等待品牌方审核'
  }
  
  return '请进行审核'
}

// 获取非审核相关的状态列表
const getNonReviewStatuses = () => {
  const currentStatus = taskDetail.value?.task_status
  
  // 确保availableStatuses.value存在
  if (!availableStatuses.value || !Array.isArray(availableStatuses.value)) {
    return []
  }
  
  // 状态和按钮的映射关系
  const statusButtonMap = {
    'rejected': ['cancelled'], // 审核拒绝状态只显示取消按钮
    'approved': ['running', 'cancelled'], // 审核通过状态显示运行中和取消按钮
    'running': ['completed', 'cancelled'] // 运行中状态显示完成和取消按钮
  }
  
  // 如果当前状态在映射关系中，使用映射的按钮
  if (statusButtonMap[currentStatus]) {
    return availableStatuses.value.filter(status => 
      statusButtonMap[currentStatus].includes(status)
    ).sort((a, b) => {
      // 按照映射顺序排序
      const order = statusButtonMap[currentStatus]
      return order.indexOf(a) - order.indexOf(b)
    })
  }
  
  // 如果当前状态是已分配或待审核，并且显示了审核按钮，则不显示其他非审核按钮
  if ((currentStatus === 'assigned' || currentStatus === 'unapproved') && shouldShowReviewButtons()) {
    return []
  }
  
  return availableStatuses.value.filter(status => !isReviewStatus(status))
}

const changeTaskStatus = async (newStatus) => {
  try {
    statusLoading.value = true

    // 如果是审核相关操作，检查当前状态
    if (newStatus === 'approved' || newStatus === 'rejected') {
      if (taskDetail.value?.task_status !== 'unapproved' && taskDetail.value?.task_status !== 'assigned') {
        ElMessage.error('当前任务状态不允许进行审核操作')
        return
      }
      
      // 如果是从已分配状态进行审核，需要确保有KOL提交的资料
      if (taskDetail.value?.task_status === 'assigned' && !taskDetail.value?.published_links) {
        ElMessage.error('KOL尚未提交资料，无法进行审核')
        return
      }
    }

    // 如果是审核通过，需要确认
    if (newStatus === 'approved') {
      await ElMessageBox.confirm(
        '确定要通过这个任务吗？',
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    const response = await apiService.changeTaskStatus({
      task_id: props.task.id,
      new_status: newStatus,
      type: 1, // Bit操作
    })

    ElMessage.success(response.data.message || '状态更新成功')

    // 刷新任务详情
    await fetchTaskDetail()
    
    // 刷新可用状态和邀请申请状态
    await fetchAvailableStatuses()
    await checkInvitationsAndApplications()

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.task.id,
      oldStatus: taskDetail.value?.task_status,
      newStatus: newStatus
    })

  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作
      console.error('更新状态失败:', error)
      ElMessage.error(error.response?.data?.detail || '更新状态失败')
    }
  } finally {
    statusLoading.value = false
  }
}

// 显示拒绝对话框
const showRejectDialog = () => {
  rejectReason.value = ''
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入拒绝理由')
    return
  }

  // 检查当前任务状态是否允许审核
  if (taskDetail.value?.task_status !== 'unapproved' && taskDetail.value?.task_status !== 'assigned') {
    ElMessage.error('当前任务状态不允许进行审核操作')
    return
  }
  
  // 如果是从已分配状态进行审核，需要确保有KOL提交的资料
  if (taskDetail.value?.task_status === 'assigned' && !taskDetail.value?.published_links) {
    ElMessage.error('KOL尚未提交资料，无法进行审核')
    return
  }

  try {
    statusLoading.value = true

    const response = await apiService.changeTaskStatus({
      task_id: props.task.id,
      new_status: 'rejected',
      type: 1, // Bit操作
      feedback: rejectReason.value.trim()
    })

    ElMessage.success('审核拒绝成功')

    // 关闭对话框
    rejectDialogVisible.value = false
    rejectReason.value = ''

    // 刷新任务详情
    await fetchTaskDetail()
    
    // 刷新可用状态和邀请申请状态
    await fetchAvailableStatuses()
    await checkInvitationsAndApplications()

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.task.id,
      oldStatus: taskDetail.value?.task_status,
      newStatus: 'rejected'
    })

  } catch (error) {
    console.error('审核拒绝失败:', error)
    ElMessage.error(error.response?.data?.detail || '审核拒绝失败')
  } finally {
    statusLoading.value = false
  }
}


</script>

<style scoped>
.task-detail-dialog {
  .el-empty {
    height: 150px;
  }
}

.loading {
  padding: 40px;
}

.detail-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f6f9f8;
  border: 1px solid #efefef;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #efefef;
}

.section-icon {
  color: #409EFF;
  font-size: 20px;
  margin-right: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 10px;
  background: #fff;
  border: 1px solid #efefef;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.label {
  color: #888;
  font-weight: 500;
  min-width: 80px;
  margin-right: 10px;
}

.value {
  flex: 1;
}

.value.reward {
  color: #67C23A;
  font-weight: bold;
}

.value.description,
.value.materials {
  margin-top: 8px;
  line-height: 1.6;
  color: #ddd;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  padding: 20px;
  text-align: center;
  color: #fff;
  background: linear-gradient(to bottom right, #57c9f1, #6f9fe3);

  &.card2 {
    background: linear-gradient(to bottom right, #e74888, #bc53a1);
  }

  &.card3 {
    background: linear-gradient(to bottom right, #825dbf, #5345b4);
  }

  &.card4 {
    background: linear-gradient(to bottom right, #fbb728, #f68254);
  }
}

.stats-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
}


/* 状态操作按钮样式 */
.status-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

/* 可执行操作按钮样式 */
.executable-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.status-action-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  min-width: 120px;
}

.status-action-btn {
  min-width: 100px;
}

.status-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.review-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.reject-dialog-content {
  padding: 20px 0;
}

.reject-tip {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.status-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  max-width: 200px;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .status-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .executable-actions {
    flex-direction: column;
    gap: 12px;
  }

  .status-action-item {
    width: 100%;
    align-items: center;
  }

  .status-action-btn {
    width: 100%;
    justify-content: center;
  }

  .status-description {
    text-align: center;
    max-width: none;
  }
}

.no-data {
  padding: 40px;
  text-align: center;
}

.stats-table {
  margin-top: 24px;
}

.stats-table h4 {
  margin-bottom: 16px;
  font-size: 16px;
}

.channel-summary {
  margin-bottom: 24px;
}

.channel-summary h4 {
  color: #409EFF;
  margin-bottom: 16px;
  font-size: 16px;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.material-item {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.material-image .el-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.material-video .video-player {
  width: 100%;
  max-height: 150px;
}

.material-link {
  padding: 16px;
  word-break: break-all;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

/* 草稿内容样式 - 与原页面保持统一 */
.draft-content-section {
  margin-top: 8px;
  border: 1px solid #efefef;
  border-radius: 6px;
  background: #f6f9f8;
  padding: 16px;
}

.draft-text-box {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-line;
}

.creation-notes-box {
  margin-bottom: 12px;
}

.notes-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.notes-content {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 10px;
  color: #555;
  line-height: 1.5;
  font-size: 13px;
}

.materials-box {
  margin-bottom: 12px;
}

.materials-title {
  font-weight: bold;
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.materials-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.materials-preview .material-item {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.material-image .el-image {
  width: 100%;
  height: 120px;
}

.material-video .video-player {
  width: 100%;
  max-height: 120px;
}

.material-link {
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  word-break: break-all;
}

.material-icon {
  margin-bottom: 4px;
  font-size: 16px;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background: #f8f9fa;
  color: #6c757d;
}



/* KOL提交资料样式 */
.kol-submitted-content {
  margin-top: 8px;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
  background: #f0f8ff;
  padding: 16px;
}

.submitted-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e1f5fe;
}

.submitted-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 14px;
}

.submit-time {
  font-size: 12px;
  color: #666;
}

.submitted-links-box {
  background: #fff;
  border: 1px solid #e3f2fd;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
}

.submitted-link-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f5f9ff;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.submitted-link-item:last-child {
  margin-bottom: 0;
}

.submitted-link-item .link-url {
  color: #1976d2;
  text-decoration: none;
  word-break: break-all;
  flex: 1;
}

.submitted-link-item .link-url:hover {
  text-decoration: underline;
  color: #1565c0;
}

.submitted-notes {
  background: #fff;
  border: 1px solid #e3f2fd;
  border-radius: 4px;
  padding: 12px;
}

.submitted-notes .notes-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 13px;
  margin-bottom: 8px;
}

.submitted-notes .notes-content {
  color: #555;
  line-height: 1.5;
  font-size: 13px;
  background: #f8fbff;
  border-radius: 4px;
  padding: 8px;
}

.link-url {
  color: #409EFF;
  text-decoration: none;
  word-break: break-all;
  flex: 1;
}

.link-url:hover {
  text-decoration: underline;
}
</style>
