<template>
  <div class="task-detail-view">
    <div class="detail-section">
      <h3>基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">任务名称:</span>
          <span class="value">{{ task?.task_name || '未知任务' }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务类型:</span>
          <span class="value">{{ getTaskTypeLabel(task?.task_type) }}</span>
        </div>
        <div class="info-item">
          <span class="label">基础报酬:</span>
          <span class="value">${{ task?.base_reward || 0 }}</span>
        </div>
        <div class="info-item" v-if="task?.performance_rate">
          <span class="label">效果奖励:</span>
          <span class="value">${{ task?.performance_rate }}/注册</span>
        </div>
        <div class="info-item">
          <span class="label">开始时间:</span>
          <span class="value">{{ formatDate(task?.start_date) }}</span>
        </div>
        <div class="info-item">
          <span class="label">截止时间:</span>
          <span class="value">{{ formatDate(task?.end_date) }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务状态:</span>
          <el-tag :type="getStatusType(task?.task_status)" size="small">
            {{ getStatusLabel(task?.task_status) }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="detail-section" v-if="task?.description">
      <h3>任务要求</h3>
      <div class="content-box">
        <p class="description">{{ task?.description }}</p>
      </div>
    </div>

    <div class="detail-section" v-if="task?.official_materials">
      <h3>官方素材</h3>
      <div class="content-box">
        <p class="materials">{{ task?.official_materials }}</p>
      </div>
    </div>

    <div class="detail-section" v-if="task?.published_links">
      <h3>已提交链接</h3>
      <div class="content-box">
        <div class="links-list">
          <div
            v-for="(link, index) in getLinksArray(task?.published_links)"
            :key="index"
            class="link-item"
          >
            <a :href="link" target="_blank" class="link">{{ link }}</a>
          </div>
        </div>
      </div>
    </div>

    <div class="detail-section" v-if="task?.draft_content">
      <h3>提交说明</h3>
      <div class="content-box">
        <p class="draft-content">{{ task?.draft_content }}</p>
      </div>
    </div>

    <div class="detail-section" v-if="task?.review_feedback">
      <h3>审核反馈</h3>
      <div class="content-box feedback">
        <p class="feedback-content">{{ task?.review_feedback }}</p>
      </div>
    </div>

    <div class="detail-section">
      <h3>时间记录</h3>
      <div class="timeline">
        <div class="timeline-item" v-if="task?.create_time">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-title">任务创建</div>
            <div class="timeline-time">{{ formatDateTime(task?.create_time) }}</div>
          </div>
        </div>

        <div class="timeline-item" v-if="task?.assigned_time">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-title">任务分配</div>
            <div class="timeline-time">{{ formatDateTime(task?.assigned_time) }}</div>
          </div>
        </div>

        <div class="timeline-item" v-if="task?.draft_submit_time">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-title">内容提交</div>
            <div class="timeline-time">{{ formatDateTime(task?.draft_submit_time) }}</div>
          </div>
        </div>

        <div class="timeline-item" v-if="task?.draft_reviewed_at">
          <div class="timeline-dot"></div>
          <div class="timeline-content">
            <div class="timeline-title">审核完成</div>
            <div class="timeline-time">{{ formatDateTime(task?.draft_reviewed_at) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

// 工具函数
function getTaskTypeLabel(type) {
  const labels = {
    'post': '推文',
    'video': '视频',
    'article': '文章',
    'live_stream': '直播',
    'ama_activity': 'AMA'
  }
  return labels[type] || type
}

function getStatusType(status) {
  const types = {
    'draft': 'info',
    'published': 'warning',
    'assigned': 'primary',
    'unapproved': 'info',
    'approved': 'success',
    'rejected': 'danger',
    'completed': 'success',
    'cancelled': 'info'
  }
  return types[status] || 'info'
}

function getStatusLabel(status) {
  const labels = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '执行中',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '需修改',
    'running': '运行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return labels[status] || status
}

function formatDate(dateStr) {
  if (!dateStr) return '未设置'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

function formatDateTime(dateStr) {
  if (!dateStr) return '未记录'
  return new Date(dateStr).toLocaleString('zh-CN')
}

function getLinksArray(linksStr) {
  if (!linksStr) return []
  return linksStr.split('\n').filter(link => link.trim())
}
</script>

<style scoped>
.task-detail-view {
  color: #fff;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  border-bottom: 1px solid #555;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  min-width: 80px;
  font-weight: bold;
  color: #aaa;
  margin-right: 12px;
  flex-shrink: 0;
}

.value {
  color: #fff;
  flex: 1;
}

.content-box {
  background: #23242a;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 16px;
}

.description, .materials, .draft-content {
  white-space: pre-wrap;
  line-height: 1.6;
  margin: 0;
  color: #ccc;
}

.feedback {
  border-color: #E6A23C;
  background: rgba(230, 162, 60, 0.1);
}

.feedback-content {
  color: #E6A23C;
  font-weight: 500;
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.6;
}

.links-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.link-item {
  display: flex;
  align-items: center;
}

.link {
  color: #409EFF;
  text-decoration: none;
  word-break: break-all;
}

.link:hover {
  text-decoration: underline;
}

.timeline {
  position: relative;
  padding-left: 20px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #555;
}

.timeline-item {
  position: relative;
  margin-bottom: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -12px;
  top: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409EFF;
  border: 2px solid #181818;
}

.timeline-content {
  margin-left: 16px;
}

.timeline-title {
  font-weight: bold;
  color: #fff;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #888;
}

/* Element Plus 样式覆盖 */
:deep(.el-tag) {
  color: #fff;
}
</style> 