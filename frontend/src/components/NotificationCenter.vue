<template>
  <div class="notification-center">
    <el-popover placement="bottom-end" :width="400" trigger="click" popper-class="notification-popover"
      @show="handleShow">
      <template #reference>
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
          <el-button class="notification-btn" :type="unreadCount > 0 ? 'primary' : 'info'" :icon="Bell">消息中心<span v-if="unreadCount > 0">({{ unreadCount }})</span>
          </el-button>
        </el-badge>
      </template>

      <div class="notification-header">
        <h3>消息通知</h3>
        <el-button v-if="hasUnread" link @click="markAllAsRead">
          全部标为已读
        </el-button>
      </div>

      <div class="notification-content" v-loading="loading">
        <template v-if="messages.length > 0">
          <div v-for="msg in messages" :key="msg.id" class="message-item" :class="{ 'is-unread': !msg.is_read }">
            <div class="message-icon">
              <el-icon :class="getMessageIcon(msg.message_type)">
                <component :is="getMessageIconComponent(msg.message_type)" />
              </el-icon>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(msg.content)"></div>
              <div class="message-time">{{ formatTime(msg.create_time) }}</div>
            </div>
            <div class="message-actions" v-if="!msg.is_read">
              <el-button link size="small" @click="markAsRead(msg.id)">
                标为已读
              </el-button>
            </div>
          </div>
        </template>
        <div v-else class="no-messages">
          暂无消息
        </div>
      </div>

      <div class="notification-footer">
        <el-button type="text" @click="goToMessageCenter">

        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useMessageStore } from '../store/message'
import { Bell, Message, Warning, CircleCheckFilled, InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const messageStore = useMessageStore()
const loading = ref(false)

const messages = computed(() => messageStore.messages)
const unreadCount = computed(() => messageStore.unreadCount)
const hasUnread = computed(() => unreadCount.value > 0)

// 获取消息数据
const fetchMessages = async () => {
  loading.value = true
  try {
    await messageStore.fetchUnreadCount()
  } catch (error) {
    console.error('Failed to fetch messages:', error)
  } finally {
    loading.value = false
  }
}

// 标记消息已读
const markAsRead = async (messageId) => {
  try {
    await messageStore.markAsRead(messageId)
  } catch (error) {
    console.error('Failed to mark message as read:', error)
  }
}

// 标记所有消息已读
const markAllAsRead = async () => {
  try {
    await messageStore.markAllAsRead()
  } catch (error) {
    console.error('Failed to mark all messages as read:', error)
  }
}

// 弹出框显示时获取数据
const handleShow = () => {
  fetchMessages()
}

// 跳转到消息中心页面
const goToMessageCenter = () => {
  router.push('/message-center')
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  // 一小时内
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes} 分钟前`
  }

  // 24小时内
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours} 小时前`
  }

  // 超过24小时
  return date.toLocaleDateString()
}

// 格式化消息内容
const formatMessage = (content) => {
  if (!content) return ''
  // 将 emoji 文本转换为实际的 emoji
  return content
    .replace(/🎉/g, '<span class="emoji">🎉</span>')
    .replace(/📝/g, '<span class="emoji">📝</span>')
    .replace(/✅/g, '<span class="emoji">✅</span>')
    .replace(/❌/g, '<span class="emoji">❌</span>')
    .replace(/🎊/g, '<span class="emoji">🎊</span>')
    .replace(/🚫/g, '<span class="emoji">🚫</span>')
    .replace(/⏰/g, '<span class="emoji">⏰</span>')
    .replace(/🚨/g, '<span class="emoji">🚨</span>')
    .replace(/📋/g, '<span class="emoji">📋</span>')
    .replace(/📢/g, '<span class="emoji">📢</span>')
}

// 获取消息图标
const getMessageIconComponent = (type) => {
  switch (type) {
    case 'task':
      return Message
    case 'review':
      return Warning
    case 'completion':
      return CircleCheckFilled
    case 'cancellation':
      return InfoFilled
    default:
      return Message
  }
}

// 获取消息图标样式
const getMessageIcon = (type) => {
  switch (type) {
    case 'task':
      return 'message-icon-task'
    case 'review':
      return 'message-icon-review'
    case 'completion':
      return 'message-icon-completion'
    case 'cancellation':
      return 'message-icon-cancellation'
    default:
      return 'message-icon-default'
  }
}
</script>

<style scoped>
.notification-center {
  display: inline-block;
}

.notification-badge {
  margin-right: 16px;
}

.notification-btn {
  padding: 8px 16px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.notification-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 0;
}

.message-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.3s;
}

.message-item:hover {
  background-color: #f9f9f9;
}

.message-item.is-unread {
  background-color: #f0f9ff;
}

.message-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-content {
  flex-grow: 1;
  min-width: 0;
}

.message-text {
  margin-bottom: 4px;
  color: #333;
  word-break: break-word;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-actions {
  flex-shrink: 0;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.notification-footer {
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #eee;
}

.no-messages {
  padding: 32px;
  text-align: center;
  color: #999;
}

/* 消息图标样式 */
.message-icon-task {
  color: #409EFF;
}

.message-icon-review {
  color: #E6A23C;
}

.message-icon-completion {
  color: #67C23A;
}

.message-icon-cancellation {
  color: #909399;
}

.message-icon-default {
  color: #409EFF;
}

/* Emoji 样式 */
.emoji {
  font-size: 16px;
  vertical-align: middle;
  margin: 0 2px;
}

/* 自定义滚动条 */
.notification-content::-webkit-scrollbar {
  width: 6px;
}

.notification-content::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.notification-content::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
</style>
