<template>
  <div class="task-status-flow">
    <!-- 渠道码输入对话框 -->
    <ChannelCodeDialog
      v-if="channelCodeVisible"
      :task-id="props.taskId"
      @close="handleChannelCodeClose"
      @confirm="handleChannelCodeConfirm"
    />

    <!-- KOL申请选择器 -->
    <KolApplicationSelector
      v-model:visible="kolSelectorVisible"
      :task-id="props.taskId"
      @close="handleKolSelectorClose"
      @kol-selected="handleKolSelected"
    />

    <div class="current-status">
      <h4>当前状态</h4>
      <el-tag :type="getStatusType(props.currentStatus)" size="large">
        {{ currentStatusDisplay }}
      </el-tag>
    </div>

    <!-- 专门处理终态状态 -->
    <div v-if="props.currentStatus === 'completed'" class="no-actions">
      <el-alert
        title="任务已完成"
        description="该任务已经完成，无法进行进一步操作"
        type="success"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else-if="props.currentStatus === 'cancelled'" class="no-actions">
      <el-alert
        title="任务已取消"
        description="该任务已被取消，无法进行进一步操作"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 其他状态显示可执行操作 -->
    <div v-else-if="availableStatuses.length > 0" class="status-flow">
      <h4>可执行操作</h4>
      <div class="flow-buttons">
        <el-button
          v-for="status in availableStatuses"
          :key="status"
          :type="getStatusButtonType(status)"
          @click="changeStatus(status)"
          :loading="loading"
          size="default"
        >
          {{ getStatusDisplayName(status) }}
        </el-button>
      </div>
    </div>

    <!-- 加载状态或其他情况 -->
    <div v-else class="no-actions">
      <el-alert
        title="加载中..."
        description="正在获取状态信息"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <div class="status-description">
      <h4>状态流程说明</h4>
      <div class="description-content">
        <div class="status-item">
          <div class="status-header">
            <el-tag type="info" size="small">草稿</el-tag>
            <span class="status-desc">任务创建后的初始状态</span>
          </div>
          <p class="status-detail">• 任务刚创建完成，还未发布</p>
          <p class="status-detail">• 可以修改任务内容和要求</p>
          <p class="status-detail">• 下一步：发布任务到需求池</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="warning" size="small">已发布</el-tag>
            <span class="status-desc">任务已发布到需求池，等待KOL申请</span>
          </div>
          <p class="status-detail">• 任务在需求池中，KOL可以看到并申请</p>
          <p class="status-detail">• 品牌方可以主动邀请KOL参与</p>
          <p class="status-detail">• 下一步：KOL接受任务或品牌方取消</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="primary" size="small">已分配</el-tag>
            <span class="status-desc">KOL已接受任务，开始执行</span>
          </div>
          <p class="status-detail">• KOL开始准备和制作内容</p>
          <p class="status-detail">• 品牌方可以与KOL沟通需求细节</p>
          <p class="status-detail">• 下一步：KOL提交内容等待审核</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="warning" size="small">待审核</el-tag>
            <span class="status-desc">KOL已提交内容，等待品牌方审核</span>
          </div>
          <p class="status-detail">• KOL已提交初稿或内容样本</p>
          <p class="status-detail">• 品牌方需要审核内容质量和合规性</p>
          <p class="status-detail">• 下一步：通过审核或要求修改</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="success" size="small">审核通过</el-tag>
            <span class="status-desc">内容审核通过，KOL可以正式发布</span>
          </div>
          <p class="status-detail">• 内容符合要求，允许发布</p>
          <p class="status-detail">• KOL可以在各平台正式发布内容</p>
          <p class="status-detail">• 下一步：KOL发布完成，任务结束</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="danger" size="small">审核拒绝</el-tag>
            <span class="status-desc">内容需要修改，KOL需要重新制作</span>
          </div>
          <p class="status-detail">• 内容不符合要求，需要修改</p>
          <p class="status-detail">• 查看审核意见，按要求调整内容</p>
          <p class="status-detail">• 下一步：修改后重新提交审核</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="success" size="small">已完成</el-tag>
            <span class="status-desc">任务圆满完成</span>
          </div>
          <p class="status-detail">• KOL已完成内容发布</p>
          <p class="status-detail">• 任务目标达成，进入结算流程</p>
          <p class="status-detail">• 状态：终态，无法再变更</p>
        </div>

        <div class="status-item">
          <div class="status-header">
            <el-tag type="info" size="small">已取消</el-tag>
            <span class="status-desc">任务被取消</span>
          </div>
          <p class="status-detail">• 任务因各种原因被取消</p>
          <p class="status-detail">• 不再继续执行</p>
          <p class="status-detail">• 状态：终态，无法再变更</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import apiService from '@/utils/api'
import ChannelCodeDialog from './ChannelCodeDialog.vue'
import KolApplicationSelector from './KolApplicationSelector.vue'

// 先定义状态显示名称映射函数
const getStatusDisplayName = (status) => {
  const statusNames = {
    'draft': '草稿',
    'published': '已发布',
    'assigned': '已分配',
    'unapproved': '待审核',
    'approved': '审核通过',
    'rejected': '审核拒绝待修改',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusNames[status] || status
}

const props = defineProps({
  taskId: {
    type: Number,
    required: true
  },
  currentStatus: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['status-changed'])

const loading = ref(false)
const currentStatusDisplay = ref('')
const availableStatuses = ref([])
const channelCodeVisible = ref(false)
const pendingStatusChange = ref(null)
const pendingKolId = ref(null)

// 初始化状态显示
if (props.currentStatus) {
  currentStatusDisplay.value = getStatusDisplayName(props.currentStatus)
}

// 状态颜色映射
const statusTypeMap = {
  'draft': 'info',
  'published': 'warning',
  'assigned': 'primary',
  'unapproved': 'warning',
  'approved': 'success',
  'rejected': 'danger',
  'completed': 'success',
  'cancelled': 'info'
}

// 按钮类型映射
const buttonTypeMap = {
  'draft': 'primary',
  'published': 'success',
  'assigned': 'warning',
  'unapproved': 'success',
  'approved': 'success',
  'rejected': 'warning',
  'completed': 'info',
  'cancelled': 'danger'
}

const getStatusType = (status) => {
  return statusTypeMap[status] || 'info'
}

const getStatusButtonType = (status) => {
  return buttonTypeMap[status] || 'primary'
}

// KOL选择器相关状态
const kolSelectorVisible = ref(false)

// 处理KOL选择器关闭
const handleKolSelectorClose = () => {
  kolSelectorVisible.value = false
}

const handleKolSelected = async (kolId) => {
  console.log('[TaskStatusFlow] handleKolSelected called with kolId:', kolId);
  pendingKolId.value = kolId; // 保存选择的 KOL ID
  kolSelectorVisible.value = false; // 关闭选择器

  try {
    console.log('[TaskStatusFlow] Updating task status to assigned');
    await apiService.changeTaskStatus({
      task_id: props.taskId,
      new_status: 'assigned', // 设置为 assigned
      type: 1, // Bit操作
      kol_id: pendingKolId.value,
    });

    console.log('[TaskStatusFlow] Updating invitation status to accepted');
    await apiService.updateInvitationStatus({
      task_id: props.taskId,
      kol_id: pendingKolId.value,
      status: 'accepted', // 设置为 accepted
    });

    ElMessage.success('任务状态已更新为已分配，邀请状态已更新为已接受');
    
    // 刷新状态流转信息
    await fetchStatusFlow();
    
    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.taskId,
      oldStatus: props.currentStatus,
      newStatus: 'assigned'
    });
    
  } catch (error) {
    console.error('[TaskStatusFlow] Error updating status:', error);
    ElMessage.error('更新任务或邀请状态失败');
  }
};

// 获取状态流转信息
const fetchStatusFlow = async () => {
  try {
    console.log(`[TaskStatusFlow] Fetching for task ${props.taskId}, current status: ${props.currentStatus}`)

    const response = await apiService.getBitTaskStatusFlow(props.taskId)
    console.log(`[TaskStatusFlow] API returned:`, response.data)

    // 确保状态一致性 - 优先使用API返回的当前状态
    const apiCurrentStatus = response.data.current_status
    const apiAvailableStatuses = response.data.available_statuses || []

    // 如果API返回的状态与传入的状态不一致，使用API的状态
    const actualCurrentStatus = apiCurrentStatus || props.currentStatus

    console.log(`[TaskStatusFlow] Using status: ${actualCurrentStatus}, available: [${apiAvailableStatuses.join(', ')}]`)

    currentStatusDisplay.value = getStatusDisplayName(actualCurrentStatus)
    
    // 过滤掉审核相关的状态（审核通过和审核拒绝）
    const filteredStatuses = apiAvailableStatuses.filter(status => 
      status !== 'approved' && status !== 'rejected'
    )
    availableStatuses.value = filteredStatuses

    // 特殊处理：如果是completed或cancelled状态，强制清空可用操作
    if (actualCurrentStatus === 'completed' || actualCurrentStatus === 'cancelled') {
      availableStatuses.value = []
      console.log(`[TaskStatusFlow] Terminal status detected, clearing available actions`)
    }

  } catch (error) {
    console.error('[TaskStatusFlow] 获取状态流转信息失败:', error)
    ElMessage.error('获取状态流转信息失败')
  }
}

const changeStatus = async (newStatus) => {
  try {
    loading.value = true

    // 如果是审核相关状态，需要确认
    if (newStatus === 'approved' || newStatus === 'rejected') {
      const action = newStatus === 'approved' ? '通过' : '拒绝'
      await ElMessageBox.confirm(
        `确定要${action}这个任务吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    // 如果是变更为"已分配"状态，需要先弹出渠道码对话框
    if (newStatus === 'assigned') {
      pendingStatusChange.value = newStatus
      channelCodeVisible.value = true
      loading.value = false
      return
    }

    const response = await apiService.changeTaskStatus({
      task_id: props.taskId,
      new_status: newStatus,
      type: 1, // Bit操作
      kol_id: newStatus === 'assigned' ? pendingKolId.value : undefined
    })

    ElMessage.success(response.data.message || '状态更新成功')

    // 更新当前状态显示
    currentStatusDisplay.value = getStatusDisplayName(newStatus)

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.taskId,
      oldStatus: props.currentStatus,
      newStatus: newStatus
    })

    // 刷新状态流转信息
    await fetchStatusFlow()
  } catch (error) {
    if (error !== 'cancel') { // 用户取消操作
      console.error('更新状态失败:', error)
      ElMessage.error(error.response?.data?.detail || '更新状态失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理渠道码对话框关闭
const handleChannelCodeClose = () => {
  channelCodeVisible.value = false
  pendingStatusChange.value = null
  pendingKolId.value = null
}

// 继续状态变更流程
const continueStatusChange = async () => {
  try {
    console.log('[TaskStatusFlow] continueStatusChange called')
    console.log('[TaskStatusFlow] pendingStatusChange:', pendingStatusChange.value)
    console.log('[TaskStatusFlow] pendingKolId:', pendingKolId.value)

    loading.value = true

    // 先弹出渠道码输入对话框
    channelCodeVisible.value = true
    console.log('[TaskStatusFlow] Channel code dialog opened')
  } catch (error) {
    console.error('继续状态变更失败:', error)
    ElMessage.error('状态变更失败')
    loading.value = false
  }
}

// 处理渠道码确认
const handleChannelCodeConfirm = async (data) => {
  try {
    loading.value = true
    channelCodeVisible.value = false

    // 先更新渠道码
    const channelCodeResponse = await apiService.updateTaskChannelCode({
      task_id: data.taskId,
      channel_code: data.channel_code,
      remark: data.remark
    })

    // 如果状态是 assigned，需要选择KOL
    if (pendingStatusChange.value === 'assigned') {
      kolSelectorVisible.value = true
      loading.value = false
      return
    }

    // 如果不是 assigned 状态，直接更新任务状态
    const statusResponse = await apiService.changeTaskStatus({
      task_id: props.taskId,
      new_status: pendingStatusChange.value,
      type: 1, // Bit操作
      kol_id: pendingKolId.value
    })

    ElMessage.success('渠道码设置成功，状态更新成功')

    // 更新当前状态显示
    currentStatusDisplay.value = getStatusDisplayName(pendingStatusChange.value)

    // 触发状态变更事件
    emit('status-changed', {
      taskId: props.taskId,
      oldStatus: props.currentStatus,
      newStatus: pendingStatusChange.value
    })

    // 刷新状态流转信息
    await fetchStatusFlow()

    // 清空待处理的状态变更
    pendingStatusChange.value = null

  } catch (error) {
    console.error('更新渠道码或状态失败:', error)
    ElMessage.error(error.response?.data?.detail || '更新失败')
  } finally {
    loading.value = false
  }
}

// 监听currentStatus的变化
watch(() => props.currentStatus, (newStatus) => {
  console.log(`[TaskStatusFlow] Status changed from props: ${newStatus}`)
  currentStatusDisplay.value = getStatusDisplayName(newStatus)
  // 重新获取状态流转信息
  fetchStatusFlow()
})

onMounted(() => {
  console.log(`[TaskStatusFlow] Component mounted for task ${props.taskId}, status: ${props.currentStatus}`)
  if (props.taskId && props.currentStatus) {
    currentStatusDisplay.value = getStatusDisplayName(props.currentStatus)
    fetchStatusFlow()
  } else {
    console.warn(`[TaskStatusFlow] Missing required props: taskId=${props.taskId}, currentStatus=${props.currentStatus}`)
  }
})
</script>

<style scoped>
.task-status-flow {
  border-radius: 12px;
  h4 {
    margin: 0;
  }
}

.current-status {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  .el-tag {
    margin-left: 12px;
  }
}

.current-status h4 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.status-flow {
  margin-bottom: 20px;
  padding: 12px;
  border-radius: 4px;
  background: #f6f9f8;
  border: #efefef 1px solid;
  border-left: 4px solid #67c23a;
}

.status-flow h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.flow-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.flow-buttons .el-button {
  font-weight: 500;
}

.status-description {
  margin-bottom: 20px;
  margin-top: 32px;
}

.status-description h4 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.description-content {
  background: #fafbfc;
  padding: 12px;
  border-radius: 2px;
  border: 1px solid #ebeef5;
}

.status-item {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #dcdfe6;
}

.status-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status-header .el-tag {
  margin-right: 12px;
  font-weight: 500;
}

.status-desc {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.status-detail {
  margin: 4px 0;
  margin-left: 0px;
  font-size: 13px;
  color: #909399;
  line-height: 1.6;
}

.no-actions {
  margin-bottom: 24px;
}

.no-actions .el-alert {
  border-radius: 8px;
}
</style>
