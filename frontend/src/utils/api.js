// API 工具类 - 统一管理所有API调用
import axios from 'axios'
import config from '../config'

// 创建axios实例
const api = axios.create({
  baseURL: config.API_BASE_URL,
  timeout: 30000, // 30秒超时
})

// 添加请求拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token')

  // 🔧 增强调试信息
  console.log('=== API Request Debug ===')
  console.log('URL:', config.url)
  console.log('Method:', config.method)
  console.log('Has Token:', !!token)
  console.log('Token Preview:', token ? token.substring(0, 20) + '...' : 'none')
  console.log('Headers before:', config.headers)

  if (token && token !== 'admin-token') {
    config.headers.Authorization = `Bearer ${token}`
    console.log('Authorization header set:', `Bearer ${token.substring(0, 20)}...`)
  } else {
    console.log('No valid token found, Authorization header not set')
  }

  console.log('Final headers:', config.headers)
  console.log('========================')

  return config
}, error => {
  console.error('API Request Error:', error)
  return Promise.reject(error)
})

// 添加响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API Response:', {
      url: response.config.url,
      status: response.status,
      statusText: response.statusText
    })
    return response
  },
  error => {
    console.error('API Response Error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    })
    return Promise.reject(error)
  }
)

// API 路径常量
export const API_PATHS = {
  // 用户相关
  USER_LOGIN: '/api/user/login',
  USER_REGISTER: '/api/user/register',
  USER_PROFILE_UPDATE: '/api/user/profile',
  USER_VERIFY_STATUS: '/api/user/verify/status',
  USER_VERIFY: '/api/user/verify',
  
  // API Key 管理
  API_KEY_LIST: '/api/user/api_key/list',
  API_KEY_CREATE: '/api/user/api_key/create',
  API_KEY_UPDATE: '/api/user/api_key/update',
  
  // KOL 相关
  KOL_PROFILE: '/api/kol/profiles',
  KOL_PUBLISHED_TASKS: '/api/bit/published_tasks',
  KOL_INVITE: '/api/bit/invite_kol',
  KOL_DETAIL: '/api/kol/profile',
  KOL_CURRENT_DETAIL: '/api/kol/profile/current/detail',  // 🔧 新增：当前用户KOL详情
  KOL_PERFORMANCE: '/api/kol/performance',
  KOL_PERFORMANCE_MONTHLY: '/api/kol/performance/monthly',
  KOL_PERFORMANCE_CONTENT: '/api/kol/performance/content',
  KOL_PERFORMANCE_FILTER_OPTIONS: '/api/kol/performance/filter-options',
  
  // KOL 任务相关
  KOL_MY_APPLICATIONS: '/api/kol/my-applications',
  KOL_INVITATIONS: '/api/kol/invitations',
  KOL_MY_TASKS: '/api/kol/my-tasks',
  KOL_TASK_SUBMIT_DRAFT: '/api/kol/task',
  KOL_TASK_SUBMIT_LINKS: '/api/kol/task',
  KOL_TASK_STATS: '/api/kol/task',
  KOL_TASK_DETAIL: '/api/kol/task',
  KOL_INVITATIONS_RESPOND: '/api/kol/invitation/respond',
  
  // 任务相关
  MARKETING_TASK: '/api/marketing-task',
  MARKETING_TASK_DETAIL: '/api/marketing-task-detail',
  TASK_CHANGE_STATUS: '/api/bit/task/change_status',
  UPDATE_TASK_CHANNEL_CODE: '/api/bit/task/update_channel_code',
  UPDATE_INVITATION_STATUS: '/api/kol/update_invitation_status',
  APPROVE_APPLICATION: '/api/bit/application/approve',
  REJECT_APPLICATION: '/api/bit/application/reject',
  SEND_MESSAGE: '/api/bit/test/send-message',
  
  // 统计相关
  STATS_DASHBOARD_SUMMARY: '/api/stats/dashboard/summary',
  STATS_DASHBOARD_KOL_TOP5: '/api/stats/dashboard/kol-top5',
  
  // 结算相关
  SETTLEMENTS_KOL: '/api/settlements/kol',
  
  // 文件上传
  OSS_UPLOAD: '/api/oss/upload',
  
  // 消息相关
  MESSAGE_LIST: '/api/message/',
  MESSAGE_READ: '/api/message',
  MESSAGE_READ_ALL: '/api/message/read-all',
  MESSAGE_UNREAD_COUNT: '/api/message/unread/count',
  
  // BIT 营销任务
  BIT_MARKETING_TASK_CREATE: '/api/bit/marketing_task/create',
  BIT_MARKETING_TASK_LIST: '/api/bit/marketing_task',
  BIT_MARKETING_TASK_CHECK_NAME: '/api/bit/marketing_task/check-name',
  BIT_TASK_STATUS_FLOW: '/api/bit/task',
  BIT_TASK_INVITATIONS_AND_APPLICATIONS: '/api/bit/task',
  
  // KOL 任务操作
  KOL_TASK_APPLY: '/api/kol/task/apply',
  KOL_TASK_SUBMIT: '/api/kol/task/submit',
  KOL_TASK_SUBMIT_CONTENT: '/api/kol/task/submit-content',
  KOL_TASKS_PUBLISHED: '/api/kol/tasks/published',
  KOL_TASK_BASE: '/api/kol/task',
  
  // Twitter 相关
  TWITTER_OAUTH_URL: '/api/kol/twitter/oauth/url',
  TWITTER_SYNC: '/api/kol/twitter/sync',
  TWITTER_UNBIND: '/api/kol/twitter/unbind',
  TWITTER_STATUS: '/api/kol/twitter/status',
  
  // 统计相关
  STATS_TASK_API: '/api/stats/task',
  STATS_TASK_CHANNEL: '/api/stats/task',
}

// API 服务对象
const apiService = {
  // 用户相关
  login: (data) => api.post(API_PATHS.USER_LOGIN, data),
  register: (data) => api.post(API_PATHS.USER_REGISTER, data),
  updateProfile: (data) => api.put(API_PATHS.USER_PROFILE_UPDATE, data),
  getVerifyStatus: () => api.get(API_PATHS.USER_VERIFY_STATUS),
  submitVerify: (data) => api.post(API_PATHS.USER_VERIFY, data),
  
  // API Key 管理
  getApiKeys: (params) => api.get(API_PATHS.API_KEY_LIST, { params }),
  createApiKey: (data) => api.post(API_PATHS.API_KEY_CREATE, data),
  updateApiKey: (data) => api.post(API_PATHS.API_KEY_UPDATE, data),
  
  // KOL 相关
  getKolProfile: () => api.get(API_PATHS.KOL_PROFILE),
  getPublishedTasks: () => api.get(API_PATHS.KOL_PUBLISHED_TASKS),
  inviteKol: (data) => api.post(API_PATHS.KOL_INVITE, data),
  getKolDetail: (kolId) => api.get(`${API_PATHS.KOL_DETAIL}/${kolId}/detail`),
  getCurrentUserKolDetail: () => api.get(API_PATHS.KOL_CURRENT_DETAIL),  // 🔧 新增：获取当前用户KOL详情
  getKolPerformance: (params) => api.get(API_PATHS.KOL_PERFORMANCE, { params }),
  getKolPerformanceMonthly: (params) => api.get(API_PATHS.KOL_PERFORMANCE_MONTHLY, { params }),
  getKolPerformanceContent: (params) => api.get(API_PATHS.KOL_PERFORMANCE_CONTENT, { params }),
  getKolPerformanceFilterOptions: (params) => api.get(API_PATHS.KOL_PERFORMANCE_FILTER_OPTIONS, { params }),
  
  // KOL 任务相关
  getKolMyApplications: () => api.get(API_PATHS.KOL_MY_APPLICATIONS),
  getKolInvitations: () => api.get(API_PATHS.KOL_INVITATIONS),
  getKolMyTasks: (params) => api.get(API_PATHS.KOL_MY_TASKS, { params }),
  submitKolTaskDraft: (taskId, data) => api.post(`${API_PATHS.KOL_TASK_SUBMIT_DRAFT}/${taskId}/submit_draft`, data),
  submitKolTaskLinks: (taskId, data) => api.post(`${API_PATHS.KOL_TASK_SUBMIT_LINKS}/${taskId}/submit_links`, data),
  submitTaskPublishLinks: (taskId, data) => api.post(`${API_PATHS.KOL_TASK_SUBMIT_LINKS}/${taskId}/submit_links`, data),
  getKolTaskStats: (taskId) => api.get(`${API_PATHS.KOL_TASK_STATS}/${taskId}/stats`),
  getKolTaskEarnings: (taskId) => api.get(`${API_PATHS.SETTLEMENTS_KOL}?task_id=${taskId}`),
  getKolTaskDetail: (taskId) => api.get(`${API_PATHS.KOL_TASK_DETAIL}/${taskId}`),
  respondKolInvitations: (data) => api.post(API_PATHS.KOL_INVITATIONS_RESPOND, data),
  
  // 任务相关
  getMarketingTasks: (params) => api.get(API_PATHS.MARKETING_TASK, { params }),
  getTaskDetail: (taskId) => api.get(`${API_PATHS.MARKETING_TASK_DETAIL}/${taskId}/detail`),
  changeTaskStatus: (data) => api.post(API_PATHS.TASK_CHANGE_STATUS, data),
  updateTaskChannelCode: (data) => api.post(API_PATHS.UPDATE_TASK_CHANNEL_CODE, data),
  updateInvitationStatus: (data) => api.post(API_PATHS.UPDATE_INVITATION_STATUS, data),
  approveApplication: (data) => api.post(API_PATHS.APPROVE_APPLICATION, data),
  rejectApplication: (data) => api.post(API_PATHS.REJECT_APPLICATION, data),
  sendMessage: (data) => api.post(API_PATHS.SEND_MESSAGE, data),
  
  // 统计相关
  getDashboardSummary: (params) => api.get(API_PATHS.STATS_DASHBOARD_SUMMARY, { params }),
  getDashboardKolTop5: (params) => api.get(API_PATHS.STATS_DASHBOARD_KOL_TOP5, { params }),
  
  // 结算相关
  getKolSettlements: (params) => api.get(API_PATHS.SETTLEMENTS_KOL, { params }),
  
  // 文件上传
  uploadFile: (data) => api.post(API_PATHS.OSS_UPLOAD, data),
  
  // 消息相关
  getMessageList: (params) => api.get(API_PATHS.MESSAGE_LIST, { params }),
  readMessage: (messageId) => api.post(`${API_PATHS.MESSAGE_READ}/${messageId}/read`),
  readAllMessages: () => api.post(API_PATHS.MESSAGE_READ_ALL),
  getUnreadMessageCount: () => api.get(API_PATHS.MESSAGE_UNREAD_COUNT),
  
  // BIT 营销任务
  createMarketingTask: (data) => api.post(API_PATHS.BIT_MARKETING_TASK_CREATE, data),
  getBitMarketingTasks: (params) => api.get(API_PATHS.BIT_MARKETING_TASK_LIST, { params }),
  checkTaskName: (name) => api.get(`${API_PATHS.BIT_MARKETING_TASK_CHECK_NAME}/${encodeURIComponent(name)}`),
  getBitTaskStatusFlow: (taskId) => api.get(`${API_PATHS.BIT_TASK_STATUS_FLOW}/${taskId}/status_flow`),
  getTaskInvitationsAndApplications: (taskId) => api.get(`${API_PATHS.BIT_TASK_INVITATIONS_AND_APPLICATIONS}/${taskId}/invitations-and-applications`),
  
  // KOL 任务操作
  applyKolTask: (data) => api.post(API_PATHS.KOL_TASK_APPLY, data),
  submitKolTask: (data) => api.post(API_PATHS.KOL_TASK_SUBMIT, data),
  submitKolTaskContent: (data) => api.post(API_PATHS.KOL_TASK_SUBMIT_CONTENT, data),
  getPublishedKolTasks: (params) => api.get(API_PATHS.KOL_TASKS_PUBLISHED, { params }),
  applyKolTaskById: (taskId, data) => api.post(`${API_PATHS.KOL_TASK_BASE}/${taskId}/apply`, data),
  // Twitter 相关
  getTwitterOAuthUrl: () => api.get(API_PATHS.TWITTER_OAUTH_URL),
  syncTwitter: () => api.post(API_PATHS.TWITTER_SYNC),
  unbindTwitter: () => api.delete(API_PATHS.TWITTER_UNBIND),
  getTwitterStatus: () => api.get(API_PATHS.TWITTER_STATUS),
  
  // 统计相关
  getTaskApiStats: (taskId) => api.get(`${API_PATHS.STATS_TASK_API}/${taskId}/api-stats`),
  getTaskChannelStats: (taskId, params) => api.get(`${API_PATHS.STATS_TASK_CHANNEL}/${taskId}/channel-stats`, { params }),
  
  // 额外的KOL配置文件API
  createKolProfile: (data) => api.post('/api/kol/profile', data),
  updateKolProfile: (data) => api.post('/api/kol/profile', data), // 统一使用 POST 接口进行 upsert 操作
  
  // 用户结算相关
  getUserSettlements: () => api.get('/api/settlements/user'),
  
  // 通用方法
  get: (url, params) => api.get(url, { params }),
  post: (url, data) => api.post(url, data),
  put: (url, data) => api.put(url, data),
  delete: (url) => api.delete(url),
}

// 获取上传URL（用于el-upload等组件）
export const getUploadUrl = () => config.getUploadUrl()

// 获取完整API URL（用于需要完整URL的场景）
export const getFullApiUrl = (path) => config.getApiUrl(path)

export default apiService 