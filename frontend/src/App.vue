<script setup>
import { ref, computed, onMounted } from 'vue'
import { Tickets, User, DataAnalysis, Coin, Key, SwitchButton, CreditCard, House, Search } from '@element-plus/icons-vue'
import Login from './components/Login.vue'
import { useRouter, useRoute } from 'vue-router'
import { useMessageStore } from './store/message.js'
import { useUserStore } from './store/user.js'
import MessageList from './components/MessageList.vue'
import NotificationCenter from './components/NotificationCenter.vue'

const userStore = useUserStore()
const isLoggedIn = computed(() => userStore.isLoggedIn)
const userInfo = computed(() => userStore.userInfo)
const router = useRouter()
const route = useRoute()
const showMsg = ref(false)
const messageStore = useMessageStore()
const unread = computed(() => messageStore.unreadCount)

// 监听弹窗打开，刷新消息列表
const openMessageDialog = () => {
  showMsg.value = true
  // 打开弹窗时刷新消息列表
  messageStore.fetchUnreadCount()
}

function handleLoginSuccess(user, isNewRegistration = false) {
  // 用户信息已经在Login组件中通过userStore设置了
  // 登录成功后获取消息数量并启动轮询
  messageStore.getUnreadCount()
  messageStore.fetchUnreadCount()
  messageStore.startPolling()

  // 如果是新注册用户，根据用户类型显示欢迎引导
  if (isNewRegistration) {
    // 延迟显示欢迎引导，确保登录状态已更新
    setTimeout(() => {
      showWelcomeGuide(user.user_type)
    }, 500)
  } else {
    // 登录后根据用户类型跳转首页
    if (user.user_type === 'kol') {
      router.push('/performance')
    } else {
      router.push('/')
    }
  }
}

function showWelcomeGuide(userType) {
  // 根据用户类型跳转到不同的欢迎页面
  if (userType === 'kol') {
    router.push('/my-tasks')  // KOL 跳转到我的任务
  } else if (userType === 'merchant') {
    router.push('/task')  // 商户跳转到任务中心
  } else {
    // 默认跳转到首页
    router.push('/')
  }
}

onMounted(() => {
  // 从localStorage恢复用户状态
  userStore.initFromStorage()

  if (userStore.isLoggedIn) {
    // 只有在已登录状态下才获取消息数量和启动轮询
    messageStore.getUnreadCount()
    messageStore.fetchUnreadCount()
    messageStore.startPolling()
  }
})

function setLogin(val) {
  if (!val) {
    // 使用用户存储的登出方法
    userStore.logout()
    // 清空消息数据
    messageStore.messages = []
    messageStore.unreadCount = 0
    router.push('/')
  }
}

function logout() {
  setLogin(false)
}
function handleUserCommand(cmd) {
  if (cmd === 'logout') logout()
  if (cmd === 'profile') router.push('/profile')
  if (cmd === 'verify') router.push('/verify')
  if (cmd === 'apikey') router.push('/apikey')
}

// merchant tabs
const merchantTabs = [
  { path: '/', label: '数据归因看板', icon: DataAnalysis },
  { path: '/task', label: '任务与协同', icon: Tickets },
  { path: '/kol', label: 'KOL库', icon: User },
  { path: '/settlement', label: '自动结算系统', icon: Coin }
]
// kol tabs
const kolTabs = [
  { path: '/performance', label: '业绩中心', icon: DataAnalysis },
  { path: '/task-market', label: '任务广场', icon: Search },
  { path: '/my-tasks', label: '我的任务', icon: Tickets },
  { path: '/asset', label: '资产与结算', icon: Coin },
  { path: '/kol-profile', label: '个人中心', icon: User }
]
// admin tabs = all
const adminTabs = [...merchantTabs, ...kolTabs]

const currentTabs = computed(() => {
  if (userInfo.value?.username === 'admin') return adminTabs
  if (userInfo.value?.user_type === 'merchant') return merchantTabs
  if (userInfo.value?.user_type === 'kol') return kolTabs
  return []
})

// 计算当前高亮菜单（支持子路由）
const activeMenu = computed(() => {
  const path = route.path
  return path;
  // if (path.startsWith('/kol-profile')) return '/kol-profile'
  // if (path.startsWith('/kol-center')) return '/kol-center'
  // if (path.startsWith('/kol')) return '/kol'
  // if (path.startsWith('/task')) return '/task'
  // if (path.startsWith('/my-tasks')) return '/my-tasks'
  // if (path.startsWith('/settlement')) return '/settlement'
  // if (path.startsWith('/performance')) return '/performance'
  // if (path.startsWith('/kol-task')) return '/kol-task'
  // if (path.startsWith('/asset')) return '/asset'
  // if (path.startsWith('/profile')) return '/profile'
  // if (path.startsWith('/apikey')) return '/apikey'
  // return '/'
})

// 计算用户认证状态
const verifyStatus = computed(() => {
  return userInfo.value?.verify_status || 'not_verified'
})

// 获取认证状态显示文本
const verifyStatusText = computed(() => {
  switch (verifyStatus.value) {
    case 'approved': return '已认证'
    case 'pending': return '审核中'
    case 'rejected': return '未通过'
    default: return '未认证'
  }
})

// 移除不需要的颜色计算属性，使用 Element Plus 内置类型
</script>

<template>
  <div>
    <template v-if="!isLoggedIn">
      <Login @login-success="handleLoginSuccess" />
    </template>
    <template v-else>
      <el-container>
        <el-aside class="slider">
          <!-- 侧边栏内容 -->
          <div class="logo">
            <img src="./assets/logo.png" />
            <span>Inflink</span>
          </div>
          <el-menu :router="true" :default-active="activeMenu" class="menu">
            <template v-for="tab in currentTabs" :key="tab.path">
              <el-menu-item :index="tab.path">
                <el-icon>
                  <component :is="tab.icon" />
                </el-icon>
                <span style="margin-left:8px;">{{ tab.label }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </el-aside>
        <el-container>
          <el-header class="header">

            <div class="header-tools">
              <!-- 消息中心 -->
              <NotificationCenter />

              <el-dropdown @command="handleUserCommand">
                <!-- 用户信息 -->
                <span class="user-profile">
                  <el-avatar size="small" style="background:#409EFF;">{{ userInfo?.username?.charAt(0)?.toUpperCase() ||
                    'A' }}</el-avatar>
                  <span class="name">{{ userInfo?.username || '未登录' }}</span>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon style="margin-right:8px;">
                        <User />
                      </el-icon>
                      用户信息
                    </el-dropdown-item>
                    <el-dropdown-item command="verify">
                      <el-icon style="margin-right:8px;">
                        <CreditCard />
                      </el-icon>
                      <span style="display:flex; align-items:center; justify-content:space-between; width:100%;">
                        <span>实名认证</span>
                        <el-tag
                          :type="verifyStatus === 'approved' ? 'success' : verifyStatus === 'pending' ? 'warning' : verifyStatus === 'rejected' ? 'danger' : 'info'"
                          size="small" style="margin-left:10px;">
                          {{ verifyStatusText }}
                        </el-tag>
                      </span>
                    </el-dropdown-item>
                    <!-- 只有非KOL用户才显示API令牌 -->
                    <el-dropdown-item v-if="userInfo?.user_type !== 'kol'" command="apikey">
                      <el-icon style="margin-right:8px;">
                        <Key />
                      </el-icon>
                      API 令牌
                    </el-dropdown-item>
                    <el-dropdown-item command="logout">
                      <el-icon style="margin-right:8px;">
                        <SwitchButton />
                      </el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <el-dialog v-model="showMsg" title="消息中心" width="600px">
              <MessageList />
            </el-dialog>
          </el-header>
          <el-main>
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </template>
  </div>
</template>

<style>
html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  min-height: 100vh;
  background: #f6f9f8;
  color: #444;
  font-size: 14px;
}
.el-container {
  height: 100vh;
  min-height: 100vh;
}
.slider {
  height: 100vh;
  background: #fff;
  width: 220px!important;
  box-shadow: #edf0f0 1px 0px 3px;
  position: relative;
  z-index: 9;
  .logo {
    font-size: 20px;
    font-weight: bolder;
    text-align: center;
    color: #409eff;
    padding: 15px 0;
    display: flex;
    line-height: 32px;
    img {
      width: 32px;
      height: 32px;
      margin-left: 20px;
      margin-right: 8px;
    }
  }
  .menu {
    border-right: none!important;
  }
}
.header {
  padding: 14px !important;
  border-bottom: #edf0f0 1px solid;
  flex-direction: row-reverse;
  display: flex;
  background: #fff;
  .header-tools {
    display: flex;
    align-items: center;
  }
  .user-profile {
    margin-left: 5px;
    .name {
      padding-left: 5px;
    }
  }
}
.el-main {
  /* height: calc(100vh - 56px); */
  /* min-height: 0; */
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  /* overflow: auto; */
  /* background: #181818; */
}
</style>
