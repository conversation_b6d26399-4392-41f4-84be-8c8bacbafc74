/**
 * 测试奖励类型显示修复
 */

console.log('🧪 奖励类型显示修复测试')
console.log('=' * 60)

// 模拟辅助函数
function getInferredRewardType(task) {
  // 如果有明确的 reward_type 字段，直接使用
  if (task.reward_type) {
    return task.reward_type
  }
  
  // 根据任务数据推断奖励类型
  if (task.commission_rate && task.commission_rate > 0) {
    return 'commission'  // 带单返佣
  } else if (task.performance_rate && task.performance_rate > 0) {
    return 'branding_plus_conversion'  // 品牌+转化
  } else {
    return 'branding'  // 品牌推广
  }
}

function getRewardTypeDisplayName(type) {
  const typeMap = {
    'branding': '品牌推广',
    'commission': '带单返佣',
    'branding_plus_conversion': '品牌+转化'
  }
  return typeMap[type] || '品牌推广'
}

function getRewardTypeColor(type) {
  const colorMap = {
    'branding': 'info',
    'commission': 'success',
    'branding_plus_conversion': 'warning'
  }
  return colorMap[type] || 'info'
}

// 测试用例 - 模拟可能的数据情况
const testCases = [
  {
    name: "有明确 reward_type 的任务",
    task: {
      reward_type: 'branding',
      base_reward: 100,
      performance_rate: 0,
      commission_rate: 0
    },
    expected: {
      inferredType: 'branding',
      displayName: '品牌推广',
      color: 'info'
    }
  },
  {
    name: "没有 reward_type，但有 commission_rate",
    task: {
      reward_type: null,
      base_reward: 0,
      performance_rate: 0,
      commission_rate: 15
    },
    expected: {
      inferredType: 'commission',
      displayName: '带单返佣',
      color: 'success'
    }
  },
  {
    name: "没有 reward_type，但有 performance_rate",
    task: {
      reward_type: null,
      base_reward: 50,
      performance_rate: 2.5,
      commission_rate: 0
    },
    expected: {
      inferredType: 'branding_plus_conversion',
      displayName: '品牌+转化',
      color: 'warning'
    }
  },
  {
    name: "没有 reward_type，只有 base_reward",
    task: {
      reward_type: null,
      base_reward: 100,
      performance_rate: 0,
      commission_rate: 0
    },
    expected: {
      inferredType: 'branding',
      displayName: '品牌推广',
      color: 'info'
    }
  },
  {
    name: "完全空的奖励数据",
    task: {
      reward_type: null,
      base_reward: 0,
      performance_rate: 0,
      commission_rate: 0
    },
    expected: {
      inferredType: 'branding',
      displayName: '品牌推广',
      color: 'info'
    }
  }
]

console.log('\n🔧 修复内容:')
console.log('1. ✅ 添加 getInferredRewardType 函数智能推断奖励类型')
console.log('2. ✅ 根据 commission_rate 和 performance_rate 推断类型')
console.log('3. ✅ 提供默认的品牌推广类型')
console.log('4. ✅ 更新模板使用推断的奖励类型')

console.log('\n📋 推断逻辑测试:')

let passed = 0
let total = testCases.length

testCases.forEach((testCase, index) => {
  const task = testCase.task
  const expected = testCase.expected
  
  // 测试推断逻辑
  const inferredType = getInferredRewardType(task)
  const displayName = getRewardTypeDisplayName(inferredType)
  const color = getRewardTypeColor(inferredType)
  
  const results = {
    inferredType,
    displayName,
    color
  }
  
  const success = JSON.stringify(results) === JSON.stringify(expected)
  
  console.log(`\n${index + 1}. ${testCase.name}`)
  console.log(`   原始数据: reward_type=${task.reward_type}, base_reward=${task.base_reward}, performance_rate=${task.performance_rate}, commission_rate=${task.commission_rate}`)
  console.log(`   推断类型: ${inferredType}`)
  console.log(`   显示名称: "${displayName}"`)
  console.log(`   标签颜色: ${color}`)
  console.log(`   测试结果: ${success ? '✅ 通过' : '❌ 失败'}`)
  
  if (success) passed++
})

console.log('\n' + '=' * 60)
console.log(`测试总结: ${passed}/${total} 通过`)

if (passed === total) {
  console.log('🎉 所有测试通过！奖励类型显示修复成功！')
  
  console.log('\n📝 修复效果:')
  console.log('现在即使任务数据中没有 reward_type 字段，也能正确显示奖励类型：')
  console.log('- 有 commission_rate > 0 → 显示 "带单返佣" (绿色)')
  console.log('- 有 performance_rate > 0 → 显示 "品牌+转化" (橙色)')
  console.log('- 其他情况 → 显示 "品牌推广" (蓝色)')
  
  console.log('\n✨ 智能推断规则:')
  console.log('1. 优先使用明确的 reward_type 字段')
  console.log('2. 根据 commission_rate 判断是否为带单返佣')
  console.log('3. 根据 performance_rate 判断是否为品牌+转化')
  console.log('4. 默认为品牌推广模式')
} else {
  console.log('⚠️ 部分测试失败，需要检查逻辑')
}
