from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAP<PERSON>, Request, HTTPException, Response, Depends, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from datetime import datetime, timezone
from dotenv import load_dotenv
from utils.config import config, EnvMode
import asyncio
from utils.logger import logger, structlog
import time
from collections import OrderedDict
from typing import Dict, Any
from jobs import start_scheduler

from pydantic import BaseModel, ValidationError
from sqlalchemy.exc import SQLAlchemyError
import uuid
# Import the agent API module

import sys

# 导入异常处理器
from utils.exceptions import (
    BusinessException,
    business_exception_handler,
    sqlalchemy_exception_handler,
    validation_exception_handler,
    http_exception_handler,
    general_exception_handler
)


load_dotenv()

uvicorn.config.LOOP_SETUPS = {
    "none": None,
    "auto": "uvicorn.loops.asyncio:asyncio_setup",
    "asyncio": "uvicorn.loops.asyncio:asyncio_setup",
    "uvloop": "uvicorn.loops.uvloop:uvloop_setup",
}


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    # do something here on server startup
    logger.info('Starting up...')
    # 启动定时任务
    scheduler = start_scheduler()
    yield
    # do something here on server shutdown
    logger.info("Shutting down...")
    scheduler.shutdown() 

app = FastAPI(
    docs_url="/docs",
    redoc_url=None,
    lifespan=app_lifespan
)

# 健康检查端点
@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "kol-hub-backend"
    }

# 注册全局异常处理器
app.add_exception_handler(BusinessException, business_exception_handler)
app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
app.add_exception_handler(ValidationError, validation_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

@app.middleware("http")
async def log_requests_middleware(request: Request, call_next):
    structlog.contextvars.clear_contextvars()

    request_id = str(uuid.uuid4())
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"
    method = request.method
    path = request.url.path
    query_params = str(request.query_params)

    structlog.contextvars.bind_contextvars(
        request_id=request_id,
        client_ip=client_ip,
        method=method,
        path=path,
        query_params=query_params
    )

    # Log the incoming request
    logger.info(f"Request started: {method} {path} from {client_ip} | Query: {query_params}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.debug(f"Request completed: {method} {path} | Status: {response.status_code} | Time: {process_time:.2f}s")
        return response
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"Request failed: {method} {path} | Error: {str(e)} | Time: {process_time:.2f}s")
        raise

# Define allowed origins based on environment
allowed_origins = ["https://www.suna.so", "https://suna.so"]
allow_origin_regex = None

# Add local development origins
if config.ENV_MODE == EnvMode.LOCAL:
    allowed_origins.append("http://localhost:8002")
    allowed_origins.append("http://localhost:8080")
    allowed_origins.append("http://localhost:8081")  # 前端开发服务器
    allowed_origins.append("http://localhost:8082")  # 前端开发服务器（备用端口）
    allowed_origins.append("http://127.0.0.1:8080")
    allowed_origins.append("http://127.0.0.1:8081")
    allowed_origins.append("http://127.0.0.1:8082")
    # 添加更多可能的开发端口
    for port in range(3000, 3010):
        allowed_origins.append(f"http://localhost:{port}")
        allowed_origins.append(f"http://127.0.0.1:{port}")
    for port in range(8080, 8090):
        allowed_origins.append(f"http://localhost:{port}")
        allowed_origins.append(f"http://127.0.0.1:{port}")

# Add staging-specific origins
if config.ENV_MODE == EnvMode.STAGING:
    allowed_origins.append("http://localhost:8002")
    allowed_origins.append("http://localhost:8080")
    allowed_origins.append("http://localhost:8081")  # 前端开发服务器
    allowed_origins.append("http://localhost:8082")  # 前端开发服务器（备用端口）
    allowed_origins.append("http://127.0.0.1:8080")
    allowed_origins.append("http://127.0.0.1:8081")
    allowed_origins.append("http://127.0.0.1:8082")
    allow_origin_regex = r"https://suna-.*-prjcts\.vercel\.app"

# 开发环境使用更宽松的CORS配置
if config.ENV_MODE == EnvMode.LOCAL:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 开发环境允许所有源
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],  # 允许所有头部
    )
else:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_origin_regex=allow_origin_regex,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["Content-Type", "Authorization", "X-Project-Id"],
    )

# Create a main API router
api_router = APIRouter()

# Add health check to API router
@api_router.get("/health")
async def health_check():
    """Health check endpoint to verify API is working."""
    logger.info("Health check endpoint called")
    return {
        "status": "ok", 
        "timestamp": datetime.now(timezone.utc).isoformat(),
        # "instance_id": instance_id
    }

# Include the main API router with /api prefix
app.include_router(api_router, prefix="/api")

from apis.bit_apis import router as bit_router
app.include_router(bit_router, prefix="/api")

from apis.kol_apis import router as kol_router
app.include_router(kol_router, prefix="/api")

from apis.user_apis import router as user_router
app.include_router(user_router, prefix="/api")

from apis.message_apis import router as message_router
app.include_router(message_router, prefix="/api")

from apis.stats_apis import router as stats_router
app.include_router(stats_router, prefix="/api")

from apis.openapi_apis import router as openapi_router
app.include_router(openapi_router, prefix="/openapi")

from apis.oss_apis import router as oss_router
app.include_router(oss_router, prefix="/api")

from apis.settlement_stats_apis import router as settlement_stats_router
app.include_router(settlement_stats_router, prefix="/api")

from apis.settlement_apis import router as settlement_router
app.include_router(settlement_router, prefix="/api")

from apis.bit_profile_apis import router as bit_profile_router
app.include_router(bit_profile_router, prefix="/api")

from apis.kol_profile_apis import router as kol_profile_router
app.include_router(kol_profile_router, prefix="/api")

from apis.twitter_apis import router as twitter_router
app.include_router(twitter_router, prefix="/api")

from apis.marketing_task_apis import router as marketing_task_router
app.include_router(marketing_task_router, prefix="/api")

if __name__ == "__main__":
    import uvicorn

    workers = 1
    
    logger.info(f"Starting server on 0.0.0.0:8002 with {workers} workers")
    uvicorn.run(
        "api:app", 
        host="0.0.0.0", 
        port=8002,
        workers=workers,
        loop="asyncio"
    )