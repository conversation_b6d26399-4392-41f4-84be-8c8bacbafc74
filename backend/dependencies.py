"""
依赖注入配置
提供统一的数据库会话管理和服务实例创建
"""
from functools import lru_cache
from sqlalchemy.orm import Session
from fastapi import Depends

from models.db import SessionLocal
from repositories.user_repository import UserRepository
from repositories.message_repository import MessageRepository
from repositories.marketing_task_repository import MarketingTaskRepository

# 数据库会话依赖
def get_database_session() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Repository 工厂函数
def get_user_repository(db: Session = Depends(get_database_session)) -> UserRepository:
    """获取用户仓储实例"""
    return UserRepository(db)

def get_message_repository(db: Session = Depends(get_database_session)) -> MessageRepository:
    """获取消息仓储实例"""
    return MessageRepository(db)

def get_marketing_task_repository(db: Session = Depends(get_database_session)) -> MarketingTaskRepository:
    """获取营销任务仓储实例"""
    return MarketingTaskRepository(db)

# Service 工厂函数
def get_user_service(db: Session = Depends(get_database_session)):
    """获取用户服务实例"""
    from services.user_service import UserService
    return UserService(db)

def get_message_service(db: Session = Depends(get_database_session)):
    """获取消息服务实例"""
    from services.message_service import MessageService
    return MessageService(db)

# Note: marketing_task_service contains individual functions, not a service class
# Use direct imports: from services.marketing_task_service import get_marketing_task, add_Marketing_Task, etc.

def get_kol_profile_service(db: Session = Depends(get_database_session)):
    """获取KOL资料服务实例"""
    from services.kol_profile_service import KolProfileService
    return KolProfileService(db)

def get_bit_profile_service(db: Session = Depends(get_database_session)):
    """获取BIT资料服务实例"""
    from services.bit_profile_service import BitProfileService
    return BitProfileService(db)

# 缓存配置（可选）
@lru_cache()
def get_settings():
    """获取应用配置（缓存）"""
    from utils.config import config
    return config

# Note: JWT authentication is implemented in utils/jwt.py
# Use: from utils.jwt import get_current_user
