from datetime import datetime
from typing import Optional, List, Union
from pydantic import BaseModel, field_validator

class Material(BaseModel):
    type: str  # 'image', 'video', 'link'
    url: str
    name: Optional[str] = None
    submit_time: Optional[Union[str, datetime]] = None
    
    @field_validator('submit_time', mode='before')
    @classmethod
    def validate_submit_time(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                return v
        return v

class MarketingTask(BaseModel):
    id: Optional[int] = None
    kol_id: Optional[int] = None
    task_name: str
    task_status: str = 'draft'
    task_type: str = 'post'
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    description: Optional[str] = None
    official_materials: Optional[List[Material]] = None
    channel_code: Optional[str] = None
    creator: int
    reward_type: str = 'branding'
    base_reward: float
    performance_rate: Optional[float] = 0
    commission_rate: Optional[float] = 0
    conversion_reward_per_ftt: Optional[float] = 0
    enable_conversion_reward: bool = False
    target_kol_count: int = 1
    review_feedback: Optional[str] = None
    published_links: Optional[List[Material]] = None
    published_links_metrics: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    draft_reviewed_at: Optional[datetime] = None
    draft_content: Optional[str] = None
    draft_submit_time: Optional[datetime] = None
    assigned_time: Optional[datetime] = None

class MarketingTaskCreate(BaseModel):
    task_name: str
    task_type: str = 'post'
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    description: Optional[str] = None
    official_materials: Optional[List[Material]] = None
    channel_code: Optional[str] = None
    reward_type: str = 'branding'
    base_reward: float
    performance_rate: Optional[float] = 0
    commission_rate: Optional[float] = 0
    conversion_reward_per_ftt: Optional[float] = 0
    enable_conversion_reward: bool = False
    target_kol_count: int = 1

class MarketingTaskDetail(BaseModel):
    id: int
    kol_id: Optional[int] = None
    task_name: str
    task_status: str
    task_type: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    description: Optional[str] = None
    official_materials: Optional[List[Material]] = None
    channel_code: Optional[str] = None
    creator: int
    reward_type: str
    base_reward: float
    performance_rate: Optional[float] = 0
    commission_rate: Optional[float] = 0
    conversion_reward_per_ftt: Optional[float] = 0
    enable_conversion_reward: bool = False
    target_kol_count: int = 1
    review_feedback: Optional[str] = None
    published_links: Optional[str] = None
    published_links_metrics: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    draft_reviewed_at: Optional[datetime] = None
    draft_content: Optional[str] = None
    draft_submit_time: Optional[datetime] = None
    assigned_time: Optional[datetime] = None