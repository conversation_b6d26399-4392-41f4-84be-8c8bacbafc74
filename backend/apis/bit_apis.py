import re
from fastapi import APIRouter, Request, Depends, HTTPException, Query, Body

from utils.logger import logger
from services.marketing_task_service import get_marketing_task, add_Marketing_Task, get_marketing_task_by_taskId, marketing_task_table
from services.task_status_service import TaskStatusService
from services.kol_invitation_service import KolInvitationService
from models.MarketingTask import MarketingTask, Material
from models.MarketingTaskCreate import MarketingTaskCreate
from models.user import UserInfo
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime, date
from models.Message import Message
from utils.jwt import get_current_user
from sqlalchemy import select, and_, Result, Table
from models.db import engine
from models import metadata

# 数据库表
marketing_task_table = Table('marketing_task', metadata, autoload_with=engine)

router = APIRouter(prefix="/bit", tags=["bit"])

# 状态流转规则 - 根据业务流程定义
FLOW = {
    "draft": ["published"],                    # 草稿 -> 已发布
    "published": ["assigned", "cancelled"],   # 已发布 -> 已分配/已取消
    "assigned": ["unapproved", "cancelled"],  # 已分配 -> 待审核/已取消
    "unapproved": ["approved", "rejected", "cancelled"],  # 待审核 -> 审核通过/审核拒绝待修改/已取消
    "approved": ["completed", "cancelled"],   # 审核通过 -> 已完成/已取消
    "rejected": ["unapproved", "cancelled"],  # 审核拒绝待修改 -> 待审核/已取消
    "completed": [],                          # 已完成 - 终态
    "cancelled": []                           # 已取消 - 终态
}

# KOL邀请相关模型
class KolInvitationRequest(BaseModel):
    task_id: int
    kol_id: int
    message: Optional[str] = None
    channel_code: str = Field(..., min_length=1, description="渠道码，必填")

class KolInvitationResponse(BaseModel):
    task_id: int
    kol_id: int
    invitation_id: int
    status: str
    message: str

# 消息发送请求模型
class SendMessageRequest(BaseModel):
    user_id: int
    content: str
    message_type: str = "task"

@router.get("/marketing_task")
async def marketing_task(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: int = None,  # 保持兼容性
    current_user: UserInfo = Depends(get_current_user)
):
    logger.info(f'get marketing task for user: {current_user.id}, page={page}, page_size={page_size}')
    # 优先使用JWT token中的用户ID
    actual_user_id = current_user.id if current_user else user_id
    response = get_marketing_task(actual_user_id, page, page_size)
    return response

@router.get("/kol_profile")
async def get_kol_profiles_with_status(
    current_user: UserInfo = Depends(get_current_user)
):
    """获取KOL资料列表，包含任务状态和邀请状态"""
    logger.info(f'get KOL profiles with status for user: {current_user.id}')
    try:
        response = KolInvitationService.get_kol_profiles_with_status(bit_id=current_user.id)
        return response
    except Exception as e:
        logger.error(f"获取KOL资料失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取KOL资料失败")

@router.post("/invite_kol")
async def invite_kol(
    request: KolInvitationRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """邀请KOL接受任务"""
    logger.info(f'Bit {current_user.id} inviting KOL {request.kol_id} for task {request.task_id}')
    try:
        response = KolInvitationService.invite_kol(
            task_id=request.task_id,
            kol_id=request.kol_id,
            bit_id=current_user.id,
            message=request.message,
            channel_code=request.channel_code
        )
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"邀请KOL失败: {str(e)}")
        raise HTTPException(status_code=500, detail="邀请KOL失败")

@router.get("/task/{task_id}/invitations")
async def get_task_invitations(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取任务的邀请列表"""
    logger.info(f'get invitations for task: {task_id}')
    try:
        response = KolInvitationService.get_task_invitations(task_id)
        return response
    except Exception as e:
        logger.error(f"获取任务邀请列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务邀请列表失败")

@router.get("/task/{task_id}/invitations-and-applications")
async def get_task_invitations_and_applications(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取任务的邀请列表和申请列表"""
    logger.info(f'get invitations and applications for task: {task_id}')
    try:
        # 获取邀请列表
        invitations = KolInvitationService.get_task_invitations(task_id)
        
        # 获取申请列表
        applications = KolInvitationService.get_task_applications(task_id)
        
        return {
            "invitations": invitations,
            "applications": applications
        }
    except Exception as e:
        logger.error(f"获取任务邀请和申请列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务邀请和申请列表失败")


# MarketingTaskCreate 类已从 models.MarketingTaskCreate 导入

@router.post("/marketing_task/create")
async def create_marketing_task(
    task: MarketingTaskCreate,
    current_user: UserInfo = Depends(get_current_user)
):
    logger.info(f'Create marketing task for user: {current_user.id}')
    logger.info(f'Received task data: {task.dict()}')
    logger.info(f'Official materials JSON: {task.official_materials}')

    new_task = MarketingTask(
        task_name=task.task_name,
        kol_id=task.kol_id,  # 现在可以为None
        task_status=task.task_status,
        task_type=task.task_type,
        start_date=task.start_date,
        end_date=task.end_date,
        description=task.description,
        creator=current_user.id,  # 使用JWT token中的用户ID，更安全
        channel_code=task.channel_code,
        # 奖励相关字段
        reward_type=task.reward_type,
        base_reward=task.base_reward,
        performance_rate=task.performance_rate,
        commission_rate=task.commission_rate,
        conversion_reward_per_ftt=task.conversion_reward_per_ftt,
        enable_conversion_reward=task.enable_conversion_reward,
        target_kol_count=task.target_kol_count,
        review_feedback=task.review_feedback,
        published_links=task.published_links,
        draft_content=task.draft_content,
        draft_submit_time=task.draft_submit_time,
        draft_reviewed_at=task.draft_reviewed_at,
        assigned_time=task.assigned_time,
        official_materials=task.official_materials
    )
    logger.info(f'New task official_materials: {new_task.official_materials}')
    response = add_Marketing_Task(new_task)
    return response

@router.get("/marketing_task/check-name/{task_name}")
async def check_task_name_availability(task_name: str, task_id: Optional[int] = None):
    """检查任务名称是否可用"""
    try:
        if task_id is not None:
            # 编辑模式：排除当前任务ID检查名称重复
            from services.marketing_task_service import check_task_name_exists_exclude_id
            exists = check_task_name_exists_exclude_id(task_name, task_id)
        else:
            # 创建模式：检查名称是否已存在
            from services.marketing_task_service import check_task_name_exists
            exists = check_task_name_exists(task_name)
        return {"available": not exists}
    except Exception as e:
        logger.error(f"检查任务名称可用性失败: {str(e)}")
        raise HTTPException(status_code=500, detail="检查任务名称失败")

# 添加更新营销任务的请求模型
class MarketingTaskUpdate(BaseModel):
    id: int
    task_name: str
    task_type: str = 'post'
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    description: Optional[str] = None
    official_materials: Optional[List[Material]] = None
    reward_type: str = 'branding'
    base_reward: float
    performance_rate: Optional[float] = 0
    commission_rate: Optional[float] = 0
    enable_conversion_reward: bool = False

@router.put("/marketing_task/update")
async def update_marketing_task(
    task: MarketingTaskUpdate,
    current_user: UserInfo = Depends(get_current_user)
):
    """更新营销任务（仅限草稿状态）"""
    try:
        # 检查任务是否存在且为草稿状态
        existing_task = TaskStatusService.get_task_by_id(task.id)
        if not existing_task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 检查权限（只有任务创建者可以更新）
        if existing_task['creator'] != current_user.id:
            raise HTTPException(status_code=403, detail="无权限更新此任务")

        # 检查任务状态（只有草稿状态可以更新）
        if existing_task['task_status'] != 'draft':
            raise HTTPException(status_code=400, detail="只有草稿状态的任务可以编辑")

        # 检查任务名称是否重复（排除当前任务）
        if task.task_name != existing_task['task_name']:
            from services.marketing_task_service import check_task_name_exists_exclude_id
            if check_task_name_exists_exclude_id(task.task_name, task.id):
                raise HTTPException(status_code=400, detail=f"任务名称 '{task.task_name}' 已存在，请使用其他名称")

        # 调用服务层更新任务
        from services.marketing_task_service import update_marketing_task_service
        response = update_marketing_task_service(task, current_user.id)
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新营销任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新任务失败")

class TestNotifyRequest(BaseModel):
    task_id: int
    new_status: str
    operator_id: int
    feedback: Optional[str] = None

@router.post("/test/notify-status-change")
async def test_notify_status_change(request: TestNotifyRequest):
    """测试：手动触发任务状态变更通知"""
    try:
        from services.task_status_service import TaskStatusService
        from services.task_notification_service import TaskNotificationService
        
        # 获取任务信息
        task = TaskStatusService.get_task_by_id(request.task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        old_status = task['task_status']
        
        # 直接发送通知，不更新状态
        TaskNotificationService.notify_status_change(task, old_status, request.new_status, request.operator_id, request.feedback)
        
        return {
            "success": True,
            "message": "测试通知已发送",
            "task_id": request.task_id,
            "old_status": old_status,
            "new_status": request.new_status
        }
    except Exception as e:
        logger.error(f"测试通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试通知失败: {str(e)}")

@router.post("/test/send-message")
async def test_send_message(
    request: SendMessageRequest
):
    """测试：手动发送消息给指定用户"""
    try:
        from services.message_service import MessageService, MessageCreate
        from models.db import get_db
        
        # 使用正确的数据库会话
        with get_db() as db:
            service = MessageService(db)
            message_data = MessageCreate(
                user_id=request.user_id,
                content=request.content,
                message_type=request.message_type
            )
            result = service.create_message(message_data)
        
        logger.info(f"✅ 测试消息发送成功: 用户ID={request.user_id}, 内容={request.content[:50]}...")
        
        return {
            "success": True,
            "message": "测试消息发送成功",
            "data": result.model_dump()
        }
    except Exception as e:
        logger.error(f"❌ 测试消息发送失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试消息发送失败: {str(e)}")

class TaskStatusChangeRequest(BaseModel):
    task_id: int
    new_status: str
    feedback: Optional[str] = None  # 审核反馈意见
    type: int  # 1: Bit操作, 2: KOL操作
    kol_id: Optional[int] = None  # KOL ID，用于状态变更为已分配时

class UpdateChannelCodeRequest(BaseModel):
    task_id: int
    channel_code: str
    remark: Optional[str] = None

@router.post("/task/change_status")
async def change_task_status(
    req: TaskStatusChangeRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """更新任务状态 - 使用新的任务状态服务"""
    try:
        result = TaskStatusService.update_task_status(
            task_id=req.task_id,
            new_status=req.new_status,
            operator_id=current_user.id,
            feedback=req.feedback if hasattr(req, 'feedback') else None,
            kol_id=req.kol_id
        )
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"更新任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新任务状态失败")

@router.post("/task/update_channel_code")
async def update_task_channel_code(
    req: UpdateChannelCodeRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """更新任务的渠道码"""
    try:
        from sqlalchemy import select, update
        from models.db import engine
        
        with engine.connect() as conn:
            # 查找任务
            stmt = select(marketing_task_table).where(marketing_task_table.c.id == req.task_id)
            result = conn.execute(stmt)
            task = result.first()
            
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            task_dict = dict(task._mapping)
            
            # 检查权限（只有任务创建者可以更新渠道码）
            if task_dict['creator'] != current_user.id:
                raise HTTPException(status_code=403, detail="无权限更新此任务的渠道码")
            
            # 更新渠道码
            update_data = {
                'channel_code': req.channel_code,
                'update_time': datetime.now()
            }
            
            # 如果有备注，可以记录到review_feedback字段
            if req.remark:
                update_data['review_feedback'] = f"渠道码备注: {req.remark}"
            
            stmt = (
                update(marketing_task_table)
                .where(marketing_task_table.c.id == req.task_id)
                .values(**update_data)
            )
            conn.execute(stmt)
            conn.commit()
            
            logger.info(f"任务 {req.task_id} 的渠道码已更新为: {req.channel_code}")
            
            return {
                "success": True,
                "message": "渠道码更新成功",
                "data": {
                    "task_id": req.task_id,
                    "channel_code": req.channel_code,
                    "remark": req.remark
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新渠道码失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新渠道码失败")

@router.get("/task/{task_id}/status_flow")
async def get_task_status_flow(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """获取任务的状态流转信息"""
    task = TaskStatusService.get_task_by_id(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    current_status = task['task_status']
    available_statuses = TaskStatusService.get_available_statuses(current_status)
    
    return {
        "task_id": task_id,
        "current_status": current_status,
        "current_status_display": TaskStatusService._get_status_display_name(current_status),
        "available_statuses": available_statuses,
        "available_statuses_display": [
            TaskStatusService._get_status_display_name(status) 
            for status in available_statuses
        ]
    }

@router.post("/task/{task_id}/cancel")
async def cancel_task(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """取消任务"""
    try:
        # 获取当前任务
        task = TaskStatusService.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务状态是否可以取消
        if task['task_status'] in ['cancelled', 'completed']:
            raise HTTPException(status_code=400, detail="任务已完成或已取消，无法再次取消")
        
        # 更新任务状态为已取消
        result = TaskStatusService.update_task_status(
            task_id=task_id,
            new_status='cancelled',
            operator_id=current_user.id,
            feedback="任务已被Bit方取消"
        )
        
        return {
            "success": True,
            "message": "任务已成功取消"
        }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="取消任务失败")

@router.get("/published_tasks")
async def get_published_tasks(
    current_user: UserInfo = Depends(get_current_user)
):
    """获取项目方创建的已发布任务列表（用于邀请KOL）"""
    logger.info(f'Get published tasks for bit user: {current_user.id}')
    try:
        from services.marketing_task_service import marketing_task_table
        
        with engine.connect() as conn:
            stmt = select(marketing_task_table).where(
                and_(
                    marketing_task_table.c.creator == current_user.id,
                    marketing_task_table.c.task_status == 'published'
                )
            ).order_by(marketing_task_table.c.create_time.desc())
            
            result: Result = conn.execute(stmt)
            tasks = result.fetchall()
            
            # 转换为字典格式
            result_list = []
            for task in tasks:
                task_dict = dict(task._mapping)
                result_list.append({
                    'id': task_dict['id'],
                    'task_name': task_dict['task_name'],
                    'task_type': task_dict['task_type'],
                    'task_status': task_dict['task_status'],
                    'description': task_dict['description'],
                    'base_reward': float(task_dict['base_reward']) if task_dict['base_reward'] else 0,
                    'performance_rate': float(task_dict['performance_rate']) if task_dict['performance_rate'] else 0,
                    'start_date': task_dict['start_date'].isoformat() if task_dict['start_date'] else None,
                    'end_date': task_dict['end_date'].isoformat() if task_dict['end_date'] else None,
                    'create_time': task_dict['create_time'].isoformat() if task_dict['create_time'] else None,
                    'creator': task_dict['creator'],
                    'kol_id': task_dict['kol_id']
                })
            
            return result_list
    except Exception as e:
        logger.error(f"获取已发布任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取已发布任务失败")

# === 新增申请管理API ===
@router.get("/applications")
async def get_bit_applications(
    current_user: UserInfo = Depends(get_current_user)
):
    """获取项目方收到的申请列表"""
    logger.info(f'Get applications for bit: {current_user.id}')

    applications = KolInvitationService.get_bit_applications(current_user.id)
    return applications

@router.get("/applications/count")
async def get_applications_count(
    current_user: UserInfo = Depends(get_current_user)
):
    """获取申请数量统计"""
    count = KolInvitationService.get_bit_applications_count(current_user.id)
    return count

@router.post("/application/{application_id}/approve")
async def approve_application(
    application_id: int,
    current_user: UserInfo = Depends(get_current_user)
):
    """批准申请"""
    logger.info(f'Bit {current_user.id} approving application {application_id}')

    response = KolInvitationService.approve_application(application_id, current_user.id)
    return response

@router.post("/application/{application_id}/reject")
async def reject_application(
    application_id: int,
    request: dict = Body(...),  # {"reason": "拒绝原因"}
    current_user: UserInfo = Depends(get_current_user)
):
    """拒绝申请"""
    logger.info(f'Bit {current_user.id} rejecting application {application_id}')

    reason = request.get('reason', '')
    response = KolInvitationService.reject_application(application_id, current_user.id, reason)
    return response

@router.post("/application/approve")
async def approve_application(
    req: dict,
    current_user: UserInfo = Depends(get_current_user)
):
    """批准KOL申请"""
    logger.info(f'approve application: {req}')
    try:
        task_id = req.get('task_id')
        kol_id = req.get('kol_id')
        application_id = req.get('application_id')
        
        if not all([task_id, kol_id, application_id]):
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        # 调用服务层处理批准逻辑
        result = KolInvitationService.approve_application(task_id, kol_id, application_id, current_user.id)
        
        return result
    except Exception as e:
        logger.error(f"批准申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批准申请失败")

@router.post("/application/reject")
async def reject_application(
    req: dict,
    current_user: UserInfo = Depends(get_current_user)
):
    """拒绝KOL申请"""
    logger.info(f'reject application: {req}')
    try:
        task_id = req.get('task_id')
        kol_id = req.get('kol_id')
        application_id = req.get('application_id')
        reject_reason = req.get('reject_reason')
        
        if not all([task_id, kol_id, application_id]):
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        # 调用服务层处理拒绝逻辑
        result = KolInvitationService.reject_application(task_id, kol_id, application_id, reject_reason, current_user.id)
        
        return result
    except Exception as e:
        logger.error(f"拒绝申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail="拒绝申请失败")