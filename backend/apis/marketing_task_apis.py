from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional, List
import json

from models.user import UserInfo
from models.MarketingTask import MarketingTask, MarketingTaskDetail, Material
from services.marketing_task_service import get_marketing_task_by_taskId
from utils.jwt import get_current_user
from dependencies import get_database_session
from utils.logger import logger

router = APIRouter(prefix="/marketing-task-detail", tags=["marketing task detail"])

@router.get("/{task_id}/detail", response_model=MarketingTaskDetail)
async def get_task_detail(
    task_id: int,
    current_user: UserInfo = Depends(get_current_user),
    db: Session = Depends(get_database_session)
):
    """获取营销任务详情"""
    try:
        task_data = get_marketing_task_by_taskId(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 记录原始数据用于调试
        logger.info(f"原始任务数据: {task_data}")
        logger.info(f"task_data类型: {type(task_data)}")
        logger.info(f"official_materials类型: {type(task_data.get('official_materials'))}")
        logger.info(f"official_materials值: {task_data.get('official_materials')}")
        
        # 处理official_materials字段
        if task_data.get('official_materials'):
            try:
                if isinstance(task_data['official_materials'], str):
                    # 如果是JSON字符串，解析为Material对象数组
                    logger.info(f"解析JSON字符串: {task_data['official_materials']}")
                    materials_data = json.loads(task_data['official_materials'])
                    logger.info(f"解析后的数据: {materials_data}")
                    
                    # 安全地创建Material对象
                    materials_list = []
                    for material in materials_data:
                        try:
                            # 确保material是字典格式
                            if isinstance(material, dict):
                                materials_list.append(Material(**material))
                            else:
                                logger.warning(f"跳过无效的material数据: {material}")
                        except Exception as e:
                            logger.error(f"创建Material对象失败: {str(e)}, 数据: {material}")
                            continue
                    
                    task_data['official_materials'] = materials_list
                else:
                    # 如果已经是数组，直接使用
                    logger.info(f"已经是数组格式: {task_data['official_materials']}")
                    task_data['official_materials'] = task_data['official_materials']
                logger.info(f"处理后的official_materials: {task_data['official_materials']}")
            except Exception as e:
                logger.error(f"解析official_materials失败: {str(e)}")
                logger.error(f"错误详情: {e}")
                task_data['official_materials'] = []
        else:
            task_data['official_materials'] = []
        

        logger.info(f"最终task_data: {task_data}")
        logger.info(f"task_data类型: {type(task_data)}")
        
        # 详细检查每个字段
        logger.info("=== 详细字段检查 ===")
        for key, value in task_data.items():
            logger.info(f"字段 {key}: 类型={type(value)}, 值={value}")
        
        # 尝试创建MarketingTaskDetail
        try:
            logger.info("=== 开始创建MarketingTaskDetail ===")
            task_detail = MarketingTaskDetail(**task_data)
            logger.info("MarketingTaskDetail创建成功")
            return task_detail
            
        except Exception as e:
            logger.error(f"创建MarketingTaskDetail失败: {str(e)}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"错误详情: {e}")
            raise HTTPException(status_code=500, detail="获取任务详情失败")
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"错误详情: {e}")
        raise HTTPException(status_code=500, detail="获取任务详情失败")