-- KOL Hub 数据库优化表结构
-- 基于完整优化方案重构的表定义

-- ============================================
-- 1. 用户信息表 (user_info) - 统一用户体系
-- ============================================
CREATE TABLE user_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID主键',
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '用户名，全局唯一',
    email VARCHAR(128) NOT NULL UNIQUE COMMENT '邮箱地址，全局唯一',
    password_hash VARCHAR(128) NOT NULL COMMENT '密码哈希值',
    phone VARCHAR(32) COMMENT '手机号码',
    nickname VARCHAR(64) COMMENT '用户昵称',
    avatar VARCHAR(255) COMMENT '头像URL地址',
    wallet_address VARCHAR(100) COMMENT '收款钱包地址（ERC20等）',
    user_type VARCHAR(20) NOT NULL DEFAULT 'merchant' COMMENT '用户类型：merchant(商户)、kol(主播)',
    status VARCHAR(20) DEFAULT 'active' COMMENT '账号状态：active(正常)、inactive(停用)、banned(封禁)',
    -- 实名认证字段（保留原有功能）
    real_name VARCHAR(64) COMMENT '实名姓名',
    id_type VARCHAR(32) DEFAULT 'id_card' COMMENT '证件类型：id_card=身份证，passport=护照，military_id=军官证',
    id_number VARCHAR(32) COMMENT '证件号',
    id_validity VARCHAR(128) COMMENT '证件有效期',
    is_long_term BOOLEAN DEFAULT FALSE COMMENT '是否长期有效',
    verify_status VARCHAR(20) DEFAULT 'unverified' COMMENT '认证状态：unverified(未认证)、pending(待审核)、approved(已通过)、rejected(已拒绝)',
    verify_image_url VARCHAR(255) COMMENT '认证图片',
    verify_remark VARCHAR(255) COMMENT '审核备注',
    id_card_front_url VARCHAR(255) COMMENT '身份证正面图片',
    id_card_back_url VARCHAR(255) COMMENT '身份证反面图片',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- ============================================
-- 2. BIT 信息表 (bit_profile)
-- ============================================
CREATE TABLE bit_profile (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID主键',
    user_id BIGINT NOT NULL COMMENT '关联用户ID，外键关联user_info.id',
    description TEXT COMMENT '简介',
    image_urls TEXT COMMENT '认证材料',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目方信息表';

-- ============================================
-- 3. KOL 信息表 (kol_profile)
-- ============================================
CREATE TABLE kol_profile (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID主键',
    user_id BIGINT NOT NULL COMMENT '关联用户ID，外键关联user_info.id',
    platform VARCHAR(30) COMMENT '平台信息',
    profile_url varchar(255) DEFAULT NULL COMMENT '主页/资料链接',
    platform_id VARCHAR(50) NOT NULL UNIQUE COMMENT 'platform 官方用户ID，全局唯一',
    platform_username VARCHAR(100) NOT NULL COMMENT 'platform 用户名（@后面的部分）',
    platform_name VARCHAR(100) COMMENT 'platform 显示名称',
    description TEXT COMMENT '个人简介',
    location VARCHAR(100) COMMENT '地理位置信息',
    profile_image_url VARCHAR(255) COMMENT 'platform 头像URL',
    verified BOOLEAN DEFAULT FALSE COMMENT '是否为认证账号',
    verified_type VARCHAR(20) COMMENT '认证类型：blue(蓝V)、business(企业)、government(政府)',
    account_created_at DATETIME COMMENT '社交平台账号创建时间',

    -- 公开指标
    followers_count BIGINT DEFAULT 0 COMMENT '粉丝数量',
    following_count BIGINT DEFAULT 0 COMMENT '关注数量',
    tweet_count BIGINT DEFAULT 0 COMMENT '推文总数',
    listed_count BIGINT DEFAULT 0 COMMENT '列表数量',
    like_count BIGINT DEFAULT 0 COMMENT '获赞总数',
    tag_name VARCHAR(255) NOT NULL COMMENT '标签名称，如：DeFi、NFT、GameFi、技术分析等',

    -- 数据更新时间
    last_synced_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KOL 个人信息表';

-- ============================================
-- 4. 营销任务表 (marketing_task) - 优化版本
-- ============================================
CREATE TABLE marketing_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID主键',
    kol_id BIGINT  COMMENT '关联KOL用户ID，外键关联user_info.id',
    task_name VARCHAR(255) NOT NULL UNIQUE COMMENT '任务名称，全局唯一',
    task_status VARCHAR(20) NOT NULL DEFAULT 'draft' COMMENT '任务状态：draft(草稿)、published(已发布)、assigned(已分配)、unapproved(待审核)、approved(审核通过)、rejected(审核拒绝待修改)、running(运行中)、completed(任务完成)、cancelled(任务取消)',
    task_type VARCHAR(20) NOT NULL DEFAULT 'post' COMMENT '任务类型：post(推文)、video(视频)、article(文章)、live_stream(直播)、ama_activity(AMA活动)',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    description TEXT COMMENT '任务描述、包含任务发布要求',
    official_materials TEXT COMMENT '官方素材链接',
    official_materials_json TEXT COMMENT '官方素材JSON格式，包含图片、视频和链接',
    channel_code VARCHAR(255) COMMENT '渠道码',

    -- 新增优化字段
    creator BIGINT NOT NULL COMMENT '项目方创建人ID，外键关联user_info.id',
    
    -- 奖励类型和配置
    reward_type VARCHAR(20) NOT NULL DEFAULT 'branding' COMMENT '奖励类型：branding(品牌推广)、commission(带单返佣)、branding_plus_conversion(品牌推广+按转化付费)',
    base_reward DECIMAL(10,2) NOT NULL COMMENT '基础奖励金额',
    performance_rate DECIMAL(10,2) DEFAULT 0 COMMENT '效果奖励单价（$/个）',
    commission_rate DECIMAL(5,2) DEFAULT 0 COMMENT '带单返佣比例（%）',
    conversion_reward_per_ftt DECIMAL(10,2) DEFAULT 0 COMMENT '每个FTT的转化奖励金额',
    enable_conversion_reward BOOLEAN DEFAULT FALSE COMMENT '是否启用按转化付费',
    
    target_kol_count INT DEFAULT 1 COMMENT '目标KOL数量',
    review_feedback TEXT COMMENT '审核反馈意见',
    published_links TEXT COMMENT '正式发布链接（JSON数组，存储发布链接）',
    published_links_metrics TEXT COMMENT '平台帖子指标数据（JSON数组）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    draft_reviewed_at DATETIME COMMENT '草稿审核时间',
    draft_content TEXT COMMENT '待发布内容文本、待发布内容截图、创作说明',
    draft_submit_time DATETIME COMMENT '草稿提交时间',
    assigned_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '任务分配时间'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营销任务表';

-- ============================================
-- 5. KOL邀请表 (kol_invitation)
-- ============================================
CREATE TABLE kol_invitation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '邀请ID主键',
    task_id BIGINT NOT NULL COMMENT '关联任务ID，外键关联marketing_task.id',
    kol_id BIGINT NOT NULL COMMENT '被邀请的KOL用户ID，外键关联user_info.id',
    bit_id BIGINT NOT NULL COMMENT '邀请方Bit用户ID，外键关联user_info.id',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '邀请状态：pending(待接受)、accepted(已接受)、rejected(已拒绝)、expired(已过期)',
    message TEXT COMMENT '邀请留言',
    invitation_type VARCHAR(20) DEFAULT 'invitation' COMMENT '类型：invitation(邀请)、application(申请)',
    application_reason TEXT COMMENT '申请理由（仅申请类型使用）',
    reject_reason TEXT COMMENT '拒绝原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '邀请创建时间',
    expire_time DATETIME NOT NULL COMMENT '邀请过期时间',
    response_time DATETIME COMMENT '响应时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_kol_id (kol_id),
    INDEX idx_bit_id (bit_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='KOL邀请表';

-- ============================================
-- 6. 效果数据表 kol_marketing_stats
-- ============================================
CREATE TABLE kol_marketing_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '效果数据ID主键',
    user_id BIGINT NOT NULL COMMENT '渠道/商户用户ID',
    kol_id BIGINT NOT NULL COMMENT 'KOL用户ID，外键关联user_info.id',
    task_id BIGINT NOT NULL COMMENT '关联任务分配ID，外键关联marketing_task.id',
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    -- 数据时间
    stat_date DATE NOT NULL COMMENT '统计日期（数据按天存储，如2024-06-10）',
    stat_month VARCHAR(20) NOT NULL COMMENT '统计月份（如2024-06，便于按月聚合）',
    click_count INT DEFAULT 0 COMMENT '点击量',
    register_count INT DEFAULT 0 COMMENT '注册量',
    ftt DECIMAL(18,2) DEFAULT 0 COMMENT 'FTT值',
    deposit_amount DECIMAL(18,2) DEFAULT 0 COMMENT '入金金额',
    order_amount DECIMAL(18,2) DEFAULT 0 COMMENT '带单金额',
    -- 数据来源
    data_source VARCHAR(50) DEFAULT 'api' COMMENT '数据来源：api(自动获取)、manual(手动录入)',
    channel_code VARCHAR(32) COMMENT '渠道码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='效果数据表';

-- ============================================
-- 7. 结算详情表 (settlement_detail) - 优化版本
-- ============================================
CREATE TABLE settlement_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '结算详情ID主键',
    kol_id BIGINT NOT NULL COMMENT 'KOL用户ID，外键关联user_info.id',
    bit_id INT NOT NULL COMMENT '广告主用户ID',
    task_id BIGINT NOT NULL COMMENT '关联任务分配ID，外键关联task_assignments.id',

    -- 结算信息
    settlement_month VARCHAR(7) NOT NULL COMMENT '结算月份（YYYY-MM格式，分区键）',
    settlement_date DATE COMMENT '结算日期',

    -- 费用明细
    marketing_count INT DEFAULT 0 COMMENT '营销次数',
    base_reward DECIMAL(12,2) DEFAULT 0 COMMENT '营销单价',
    base_total DECIMAL(14,2) DEFAULT 0 COMMENT '基础费用 = 营销单价 * 营销次数',
    performance_value DECIMAL(14,2) DEFAULT 0 COMMENT '效果值',
    performance_rate DECIMAL(12,2) DEFAULT 0 COMMENT '效果单价',
    performance_total DECIMAL(14,2) DEFAULT 0 COMMENT '效果佣金 = 效果值 * 效果单价',
    commission_value DECIMAL(14,2) DEFAULT 0 COMMENT '带单佣金值',
    commission_rate DECIMAL(12,2) DEFAULT 0 COMMENT '带单抽佣比例',
    commission_total DECIMAL(14,2) DEFAULT 0 COMMENT '带单佣金 = 带单佣金值 * 带单抽佣比例',
    total_fee DECIMAL(16,2) DEFAULT 0 COMMENT '费用总计',

    -- 支付信息
    status VARCHAR(20) DEFAULT 'pending' COMMENT '结算状态：pending(待支付)、paid(已支付)、failed(支付失败)',
    payment_method VARCHAR(20) DEFAULT 'crypto_wallet' COMMENT '支付方式：crypto_wallet(加密钱包)、bank_transfer(银行转账)',
    wallet_address VARCHAR(100) COMMENT '收款钱包地址',
    transaction_hash VARCHAR(100) COMMENT '交易哈希',
    payment_network VARCHAR(50) COMMENT '支付网络：ethereum、bsc、polygon等',
    paid_time DATETIME COMMENT '支付时间',
    payment_note TEXT COMMENT '支付备注',

    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算详情表';

-- ============================================
-- 8. API Key管理表 (user_api_key) - 保留原结构，优化数据类型
-- ============================================
CREATE TABLE user_api_key (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    user_id BIGINT NOT NULL COMMENT 'bit 用户ID，关联user_info(id)',
    api_key VARCHAR(64) NOT NULL UNIQUE COMMENT 'API Key',
    name VARCHAR(64) COMMENT 'API Key名称',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态(1=启用,0=禁用,2=已删除)',
    permissions VARCHAR(255) COMMENT '权限范围',
    remark VARCHAR(128) COMMENT '备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户OpenAPI Key管理表';

-- ============================================
-- 9. 站内消息表 (message) - 保留原结构，优化数据类型
-- ============================================
CREATE TABLE message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    user_id BIGINT NOT NULL COMMENT '接收用户ID',
    content VARCHAR(1024) NOT NULL COMMENT '消息内容',
    message_type VARCHAR(20) DEFAULT 'system' COMMENT '消息类型：system(系统消息)、task(任务消息)、settlement(结算消息)',
    is_read TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站内消息表';

-- ============================================
-- 10. 数据库索引优化
-- ============================================

-- 优化结算查询性能的索引
CREATE INDEX idx_settlement_kol_date ON settlement_detail(kol_id, settlement_date DESC);
CREATE INDEX idx_settlement_kol_month ON settlement_detail(kol_id, settlement_month);
CREATE INDEX idx_settlement_kol_status ON settlement_detail(kol_id, status);
CREATE INDEX idx_settlement_task_id ON settlement_detail(task_id);
CREATE INDEX idx_settlement_create_time ON settlement_detail(create_time DESC);

-- 优化营销任务查询的索引
CREATE INDEX idx_marketing_task_kol_id ON marketing_task(kol_id);
CREATE INDEX idx_marketing_task_creator ON marketing_task(creator);
CREATE INDEX idx_marketing_task_status ON marketing_task(task_status);

-- 优化效果数据查询的索引
CREATE INDEX idx_kol_stats_kol_task ON kol_marketing_stats(kol_id, task_id);
CREATE INDEX idx_kol_stats_month ON kol_marketing_stats(stat_month);
CREATE INDEX idx_kol_stats_date ON kol_marketing_stats(stat_date DESC);

-- 优化用户信息查询的索引
CREATE INDEX idx_user_info_type ON user_info(user_type);
CREATE INDEX idx_user_info_status ON user_info(status);

-- 优化KOL档案查询的索引
CREATE INDEX idx_kol_profile_user_platform ON kol_profile(user_id, platform);
CREATE INDEX idx_kol_profile_platform ON kol_profile(platform);
CREATE INDEX idx_kol_profile_tag ON kol_profile(tag_name);


-- 添加奖励类型字段
ALTER TABLE marketing_task 
ADD COLUMN reward_type VARCHAR(500) NOT NULL DEFAULT 'branding' 
COMMENT '奖励类型：branding(品牌推广)、commission(带单返佣)、branding_plus_conversion(品牌推广+按转化付费)' 
AFTER creator;

-- 修改base_reward字段的注释
ALTER TABLE marketing_task 
MODIFY COLUMN base_reward DECIMAL(10,2) NOT NULL COMMENT '基础奖励金额';

-- 添加带单返佣比例字段
ALTER TABLE marketing_task 
ADD COLUMN commission_rate DECIMAL(5,2) DEFAULT 0 
COMMENT '带单返佣比例（%）' 
AFTER performance_rate;

-- 添加每个FTT的转化奖励金额字段
ALTER TABLE marketing_task 
ADD COLUMN conversion_reward_per_ftt DECIMAL(10,2) DEFAULT 0 
COMMENT '每个FTT的转化奖励金额' 
AFTER commission_rate;

-- 添加是否启用按转化付费字段
ALTER TABLE marketing_task 
ADD COLUMN enable_conversion_reward BOOLEAN DEFAULT FALSE 
COMMENT '是否启用按转化付费' 
AFTER conversion_reward_per_ftt;


-- 添加佣金
ALTER TABLE kol_marketing_stats 
ADD COLUMN order_amount DECIMAL(18,2) DEFAULT 0 COMMENT '带单金额'
AFTER deposit_amount;

ALTER TABLE settlement_detail 
ADD COLUMN commission_value DECIMAL(14,2) DEFAULT 0 COMMENT '带单佣金值'
AFTER performance_total;

ALTER TABLE settlement_detail 
ADD COLUMN commission_rate DECIMAL(12,2) DEFAULT 0 COMMENT '带单抽佣比例'
AFTER commission_value;

ALTER TABLE settlement_detail 
ADD COLUMN commission_total DECIMAL(14,2) DEFAULT 0 COMMENT '带单佣金 = 带单佣金值 * 带单抽佣比例'
AFTER commission_rate;