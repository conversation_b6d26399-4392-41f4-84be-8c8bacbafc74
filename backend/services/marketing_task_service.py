import json
from datetime import datetime
from sqlalchemy import Date, Table, select,or_,update
from sqlalchemy.engine import Result
from models import metadata, engine
from fastapi import  HTTPException
from utils.logger import logger
from models.db import get_db
from models.MarketingTask import MarketingTask
from fastapi.encoders import jsonable_encoder

marketing_task_table = Table('marketing_task', metadata, autoload_with=engine)
user_info = Table('user_info', metadata, autoload_with=engine)

# 自定义JSON编码器，处理datetime对象
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

def get_marketing_task(user_id: int, page: int = 1, page_size: int = 20):
    """读取 marketing_task 表的内容并返回分页列表，包含KOL用户名"""
    logger.info(f"query db 「get_marketing_task」 start.. page={page}, page_size={page_size}")
    
    with engine.connect() as conn:
        # 首先获取总数
        count_stmt = select(
            marketing_task_table.c.id
        ).where(
            or_(
                marketing_task_table.c.creator == user_id,
                marketing_task_table.c.kol_id == user_id
            )
        )
        count_result = conn.execute(count_stmt)
        total = len(count_result.fetchall())
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 使用LEFT JOIN关联user_info表获取KOL用户名，添加分页
        stmt = select(
            marketing_task_table.c.id,
            marketing_task_table.c.kol_id,
            marketing_task_table.c.task_name,
            marketing_task_table.c.task_status,
            marketing_task_table.c.task_type,
            marketing_task_table.c.start_date,
            marketing_task_table.c.end_date,
            marketing_task_table.c.description,
            marketing_task_table.c.official_materials,  # 添加官方素材字段
            marketing_task_table.c.creator,
            
            # 奖励相关字段
            marketing_task_table.c.reward_type,
            marketing_task_table.c.base_reward,
            marketing_task_table.c.performance_rate,
            marketing_task_table.c.commission_rate,
            marketing_task_table.c.conversion_reward_per_ftt,
            marketing_task_table.c.enable_conversion_reward,
            
            marketing_task_table.c.target_kol_count,
            marketing_task_table.c.review_feedback,
            marketing_task_table.c.published_links,
            marketing_task_table.c.draft_content,
            marketing_task_table.c.draft_submit_time,
            marketing_task_table.c.draft_reviewed_at,
            marketing_task_table.c.assigned_time,
            marketing_task_table.c.create_time,
            marketing_task_table.c.update_time,
            # 添加KOL用户名字段
            user_info.c.username.label('kol_username')
        ).select_from(
            marketing_task_table.outerjoin(user_info, marketing_task_table.c.kol_id == user_info.c.id)
        ).where(
            or_(
                marketing_task_table.c.creator == user_id,
                marketing_task_table.c.kol_id == user_id
            )
        ).order_by(
            marketing_task_table.c.create_time.desc()
        ).offset(offset).limit(page_size)
        
        result: Result = conn.execute(stmt)
        rows = [dict(row._mapping) for row in result]
        
        logger.info(f"query db 「get_marketing_task」 end .. total={total}, items={len(rows)}")
        
        return {
            "items": rows,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }

def update_task_status(task_id: int, new_status: str, creator_id: int, kol_id: int):
    logger.info("update db 「update_task_status」 start..")
    with engine.connect() as conn:
        stmt = (
            update(marketing_task_table)
            .where(marketing_task_table.c.id == task_id)
            .values(task_status=new_status,
            creator=creator_id,
            kol_id=kol_id,
            update_time=datetime.now()
            )
        )
        conn.execute(stmt)
        conn.commit()
        logger.info("update db 「update_task_status」 end..")
def add_Marketing_Task(new_task: MarketingTask):
    logger.info(f"Adding marketing task: {new_task.task_name}")
    logger.info(f"Official materials JSON: {new_task.official_materials}")
    
    with engine.connect() as conn:
        # 检查任务名称是否已存在
        stmt = select(marketing_task_table.c.id).where(marketing_task_table.c.task_name == new_task.task_name)
        result = conn.execute(stmt)
        existing_task = result.fetchone()
        
        if existing_task:
            raise HTTPException(
                status_code=400, 
                detail=f"任务名称 '{new_task.task_name}' 已存在，请使用其他名称"
            )
        
        # 准备插入数据
        task_data = {
            'task_name': new_task.task_name,
            'kol_id': new_task.kol_id,
            'task_status': new_task.task_status,
            'task_type': new_task.task_type,
            'start_date': new_task.start_date,
            'end_date': new_task.end_date,
            'description': new_task.description,
            'official_materials': json.dumps([material.model_dump() for material in new_task.official_materials], cls=DateTimeEncoder) if new_task.official_materials else None,
            'creator': new_task.creator,
            'channel_code': new_task.channel_code,
            'reward_type': new_task.reward_type,
            'base_reward': new_task.base_reward,
            'performance_rate': new_task.performance_rate,
            'commission_rate': new_task.commission_rate,
            'conversion_reward_per_ftt': new_task.conversion_reward_per_ftt,
            'enable_conversion_reward': new_task.enable_conversion_reward,
            'target_kol_count': new_task.target_kol_count,
            'review_feedback': new_task.review_feedback,
            'published_links': new_task.published_links,
            'draft_content': new_task.draft_content,
            'draft_submit_time': new_task.draft_submit_time,
            'draft_reviewed_at': new_task.draft_reviewed_at,
            'assigned_time': new_task.assigned_time
        }
        
        logger.info(f"Task data to insert: {task_data}")
        logger.info(f"Official materials JSON string: {task_data['official_materials']}")
        
        # 插入新任务
        insert_stmt = marketing_task_table.insert().values(**task_data)
        result = conn.execute(insert_stmt)
        task_id = result.inserted_primary_key[0]
        
        try:
            conn.commit()
            logger.info(f"Successfully created task with ID: {task_id}")
        except Exception as e:
            conn.rollback()
            # 检查是否是唯一性约束错误
            if "Duplicate entry" in str(e) and "task_name" in str(e):
                raise HTTPException(
                    status_code=400, 
                    detail=f"任务名称 '{new_task.task_name}' 已存在，请使用其他名称"
                )
            else:
                logger.error(f"创建营销任务失败: {str(e)}")
                raise HTTPException(status_code=400, detail="创建任务失败，请检查输入数据")
        
        logger.info("add db 「add_Marketing_Task」 start..")
        return {"msg": "营销任务创建成功", "data": {
            "id": task_id,
            "task_name": new_task.task_name
        }}
def get_marketing_task_by_taskId(task_id: int):
    logger.info("query db 「get_marketing_task_by_taskId」 start..")
    with engine.connect() as conn:
        # 明确指定要查询的字段
        stmt = select(
            marketing_task_table.c.id,
            marketing_task_table.c.kol_id,
            marketing_task_table.c.task_name,
            marketing_task_table.c.task_status,
            marketing_task_table.c.task_type,
            marketing_task_table.c.start_date,
            marketing_task_table.c.end_date,
            marketing_task_table.c.description,
            marketing_task_table.c.official_materials,  # 添加官方素材字段
            marketing_task_table.c.channel_code,
            marketing_task_table.c.creator,
            # 奖励相关字段
            marketing_task_table.c.reward_type,
            marketing_task_table.c.base_reward,
            marketing_task_table.c.performance_rate,
            marketing_task_table.c.commission_rate,
           # marketing_task_table.c.conversion_reward_per_ftt,
            marketing_task_table.c.enable_conversion_reward,
            marketing_task_table.c.target_kol_count,
            marketing_task_table.c.review_feedback,
            marketing_task_table.c.published_links,
            marketing_task_table.c.draft_content,
            marketing_task_table.c.draft_submit_time,
            marketing_task_table.c.draft_reviewed_at,
            marketing_task_table.c.assigned_time,
            marketing_task_table.c.create_time,
            marketing_task_table.c.update_time
        ).where(marketing_task_table.c.id == task_id)
        
        result: Result = conn.execute(stmt)
        row = result.fetchone()
        
        if not row:
            return None
        
        # 转换为标准字典格式
        task_dict = dict(row._mapping)
        logger.info("query db 「get_marketing_task_by_taskId」 end.. data: %s",json.dumps(jsonable_encoder(task_dict),ensure_ascii=False))
        return task_dict

def check_task_name_exists(task_name: str) -> bool:
    """检查任务名称是否已存在"""
    logger.info(f"检查任务名称是否存在: {task_name}")
    with engine.connect() as conn:
        stmt = select(marketing_task_table.c.id).where(marketing_task_table.c.task_name == task_name)
        result = conn.execute(stmt)
        existing_task = result.fetchone()
        exists = existing_task is not None
        logger.info(f"任务名称 '{task_name}' 存在状态: {exists}")
        return exists

def get_marketing_task_by_channel_code(channel_code: str):
    """根据渠道码获取任务信息"""
    logger.info(f"query db 「get_marketing_task_by_channel_code」 start.. channel_code: {channel_code}")
    with engine.connect() as conn:
        # 明确指定需要的字段
        stmt = select(
            marketing_task_table.c.id,
            marketing_task_table.c.kol_id,
            marketing_task_table.c.task_name,
            marketing_task_table.c.channel_code,
            marketing_task_table.c.creator
        ).where(marketing_task_table.c.channel_code == channel_code)
        result = conn.execute(stmt)
        row = result.fetchone()
        marketing_task_obj = dict(row._mapping) if row else None
    logger.info(f"query db 「get_marketing_task_by_channel_code」 end.. found: {marketing_task_obj is not None}")
    return marketing_task_obj

if __name__ == "__main__":
    # 测试读取
    from pprint import pprint