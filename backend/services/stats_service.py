from fastapi import Depends
from sqlalchemy import func, desc
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta
from models import KOLMarketingStats
from models.db import get_db
from pydantic import BaseModel, Field
from fastapi import HTTPException
from typing import List
from services.marketing_task_service import get_marketing_task_by_channel_code
import structlog

logger = structlog.get_logger()

class ChannelStatsDayItem(BaseModel):
    stat_date: date = Field(..., description="统计日期")
    click_count: int = 0
    register_count: int = 0
    deposit_amount: float = 0
    order_amount: float = 0
    conversion_rate: float = 0
    ftt: float = Field(default=0, description="FTT字段")

class KOLMarketingStatsIn(BaseModel):
    user_id: int = Field(..., description="渠道/商户用户ID")
    channel_code: str = Field(..., description="渠道码")
    data: List[ChannelStatsDayItem] = Field(..., description="多天的统计数据")

def add_kol_stats(data: KOLMarketingStatsIn):
    """添加KOL统计数据"""
    with get_db() as db:
        # 根据 channel_code 获取营销任务信息
        marketing_task = get_marketing_task_by_channel_code(data.channel_code)
        if not marketing_task:
            raise HTTPException(
                status_code=400, 
                detail=f"渠道码不存在: {data.channel_code}"
            )
        
        success_count = 0
        # 处理每天的数据
        for day_data in data.data:
            stat_month = day_data.stat_date.strftime('%Y-%m')
            obj = KOLMarketingStats(
                user_id=data.user_id,
                kol_id=marketing_task['kol_id'],
                task_id=marketing_task['id'],
                task_name=marketing_task['task_name'],
                channel_code=data.channel_code,
                stat_date=day_data.stat_date,
                stat_month=stat_month,
                click_count=day_data.click_count,
                register_count=day_data.register_count,
                deposit_amount=day_data.deposit_amount,
                order_amount=day_data.order_amount,
                ftt=day_data.ftt,
                data_source='api',
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            db.merge(obj)  # 使用 merge 实现 upsert
            success_count += 1
        
        db.commit()
        return {"success": True, "processed_records": success_count}

def get_summary(user_id: int):
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        today = date.today()
        week_ago = today - timedelta(days=6)
        month_str = today.strftime('%Y-%m')

        # 通过关联marketing_task表获取用户的任务统计
        task_count = db.query(KOLMarketingStats.task_id).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.creator == user_id
        ).distinct().count()

        week_stats = db.query(
            func.sum(KOLMarketingStats.click_count),
            func.sum(KOLMarketingStats.register_count),
            func.sum(KOLMarketingStats.deposit_amount)
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.creator == user_id,
            KOLMarketingStats.stat_date >= week_ago,
            KOLMarketingStats.stat_date <= today
        ).first()
        
        month_stats = db.query(
            func.sum(KOLMarketingStats.click_count),
            func.sum(KOLMarketingStats.register_count),
            func.sum(KOLMarketingStats.deposit_amount)
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.creator == user_id,
            KOLMarketingStats.stat_month == month_str
        ).first()
        
        total_stats = db.query(
            func.sum(KOLMarketingStats.click_count),
            func.sum(KOLMarketingStats.register_count),
            func.sum(KOLMarketingStats.deposit_amount)
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.creator == user_id
        ).first()
        
        return {
            "task_count": task_count,
            "week": {
                "click": week_stats[0] or 0,
                "register": week_stats[1] or 0,
                "deposit": float(week_stats[2] or 0)
            },
            "month": {
                "click": month_stats[0] or 0,
                "register": month_stats[1] or 0,
                "deposit": float(month_stats[2] or 0)
            },
            "total": {
                "click": total_stats[0] or 0,
                "register": total_stats[1] or 0,
                "deposit": float(total_stats[2] or 0)
            }
        }

def get_kol_top5(user_id: int):
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        from models.user import UserInfo
        month_str = date.today().strftime('%Y-%m')
        
        rows = db.query(
            marketing_task_table.c.kol_id,
            UserInfo.username.label('kol_name'),
            func.sum(KOLMarketingStats.click_count).label('click_count'),
            func.sum(KOLMarketingStats.register_count).label('register_count'),
            func.sum(KOLMarketingStats.deposit_amount).label('deposit_amount')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).join(
            UserInfo, marketing_task_table.c.kol_id == UserInfo.id
        ).filter(
            marketing_task_table.c.creator == user_id,
            KOLMarketingStats.stat_month == month_str
        ).group_by(
            marketing_task_table.c.kol_id, UserInfo.username
        ).order_by(desc('register_count')).limit(5).all()
        # 日志记录生成的 SQL ，方便排查问题
        logger.info(f"query db 「get_kol_top5」 end.. sql: {db.query(KOLMarketingStats).statement}")
        result = []
        for idx, row in enumerate(rows, 1):
            # 计算转化率 = 注册数 / 点击数
            conversion_rate = (row.register_count / row.click_count * 100) if row.click_count > 0 else 0
            result.append({
                "rank": idx,
                "kol_name": row.kol_name,
                "click_count": int(row.click_count or 0),
                "register_count": int(row.register_count or 0),
                "conversion_rate": f"{conversion_rate:.2f}%",
                "deposit_amount": float(row.deposit_amount or 0)
            })
        return result

def get_kol_performance(kol_id: int, month: str = None):
    """获取KOL业绩数据，支持按月查询"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        if not month:
            month = date.today().strftime('%Y-%m')

        # 月度统计数据
        month_stats = db.query(
            func.sum(KOLMarketingStats.deposit_amount).label('income'),
            func.sum(KOLMarketingStats.click_count).label('click_count'),
            func.sum(KOLMarketingStats.register_count).label('register_count')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id,
            KOLMarketingStats.stat_month == month
        ).first()

        # 待结算金额（当月未结算的收入）
        pending_amount = db.query(
            func.sum(KOLMarketingStats.deposit_amount)
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id,
            KOLMarketingStats.stat_month == month
        ).scalar() or 0

        # 最佳推广任务
        best_tasks = db.query(
            marketing_task_table.c.task_name.label('content'),
            func.sum(KOLMarketingStats.click_count).label('click_count'),
            func.sum(KOLMarketingStats.register_count).label('register_count'),
            func.sum(KOLMarketingStats.deposit_amount).label('deposit_amount')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id,
            KOLMarketingStats.stat_month == month
        ).group_by(marketing_task_table.c.task_name)\
         .order_by(desc('deposit_amount'))\
         .limit(10).all()

        best_tasks_result = []
        for row in best_tasks:
            best_tasks_result.append({
                "content": row.content or "未知任务",
                "click_count": int(row.click_count or 0),
                "register_count": int(row.register_count or 0),
                "deposit_amount": float(row.deposit_amount or 0)
            })

        return {
            "month_income": float(month_stats.income or 0),
            "month_click_count": int(month_stats.click_count or 0),
            "month_register_count": int(month_stats.register_count or 0),
            "pending_amount": float(pending_amount),
            "best_tasks": best_tasks_result
        }

def get_kol_monthly_summary(kol_id: int):
    """获取KOL按月汇总数据"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        monthly_data = db.query(
            KOLMarketingStats.stat_month,
            func.sum(KOLMarketingStats.deposit_amount).label('total_income'),
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id
        ).group_by(KOLMarketingStats.stat_month)\
         .order_by(KOLMarketingStats.stat_month.desc())\
         .limit(12).all()

        result = []
        for row in monthly_data:
            # 计算平均转化率 = 注册数 / 点击数
            avg_conversion_rate = (row.total_registers / row.total_clicks * 100) if row.total_clicks > 0 else 0
            result.append({
                "month": row.stat_month,
                "total_income": float(row.total_income or 0),
                "total_clicks": int(row.total_clicks or 0),
                "total_registers": int(row.total_registers or 0),
                "avg_conversion_rate": float(avg_conversion_rate),
                "total_ftt": float(row.total_ftt or 0)
            })

        return result

def get_kol_content_summary(kol_id: int, month: str = None):
    """获取KOL按任务汇总数据"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        query = db.query(
            marketing_task_table.c.task_name,
            KOLMarketingStats.channel_code,
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_income'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.count(KOLMarketingStats.stat_date).label('active_days')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(marketing_task_table.c.kol_id == kol_id)

        if month:
            query = query.filter(KOLMarketingStats.stat_month == month)

        content_data = query.group_by(
            marketing_task_table.c.task_name,
            KOLMarketingStats.channel_code
        ).order_by(desc('total_income')).limit(20).all()

        result = []
        for row in content_data:
            # 计算平均转化率 = 注册数 / 点击数
            avg_conversion_rate = (row.total_registers / row.total_clicks * 100) if row.total_clicks > 0 else 0
            result.append({
                "campaign_name": row.task_name or "未知任务",
                "channel_code": row.channel_code,
                "total_clicks": int(row.total_clicks or 0),
                "total_registers": int(row.total_registers or 0),
                "total_income": float(row.total_income or 0),
                "total_ftt": float(row.total_ftt or 0),
                "avg_conversion_rate": float(avg_conversion_rate),
                "active_days": int(row.active_days or 0)
            })

        return result

def get_task_api_stats(task_id: int):
    """获取任务的API统计数据 (data_source为api)"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table

        # 获取任务基本信息
        task = db.query(marketing_task_table).filter(marketing_task_table.c.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 获取API来源的统计数据
        api_stats = db.query(
            KOLMarketingStats.stat_date,
            func.sum(KOLMarketingStats.click_count).label('daily_clicks'),
            func.sum(KOLMarketingStats.register_count).label('daily_registers'),
            func.sum(KOLMarketingStats.ftt).label('daily_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('daily_deposit')
        ).filter(
            KOLMarketingStats.task_id == task_id,
            KOLMarketingStats.data_source == 'api'
        ).group_by(KOLMarketingStats.stat_date)\
         .order_by(KOLMarketingStats.stat_date.desc()).all()

        # 计算总计
        total_stats = db.query(
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_deposit')
        ).filter(
            KOLMarketingStats.task_id == task_id,
            KOLMarketingStats.data_source == 'api'
        ).first()

        # 格式化数据
        daily_data = []
        for row in api_stats:
            daily_data.append({
                "stat_date": row.stat_date.isoformat() if row.stat_date else None,
                "daily_clicks": int(row.daily_clicks or 0),
                "daily_registers": int(row.daily_registers or 0),
                "daily_ftt": float(row.daily_ftt or 0),
                "daily_deposit": float(row.daily_deposit or 0)
            })

        return {
            "task_info": {
                "task_id": task_id,
                "task_name": task.task_name,
                "data_source": "api"
            },
            "total_stats": {
                "total_clicks": int(total_stats.total_clicks or 0),
                "total_registers": int(total_stats.total_registers or 0),
                "total_ftt": float(total_stats.total_ftt or 0),
                "total_deposit": float(total_stats.total_deposit or 0)
            },
            "daily_stats": daily_data
        }

def get_task_channel_stats(task_id: int, channel_code: str = None):
    """获取任务的渠道统计数据 (按channel_code查询)"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table

        # 获取任务基本信息
        task = db.query(marketing_task_table).filter(marketing_task_table.c.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 构建查询条件
        query_filter = [KOLMarketingStats.task_id == task_id]
        if channel_code:
            query_filter.append(KOLMarketingStats.channel_code == channel_code)

        # 获取渠道统计数据
        channel_stats = db.query(
            KOLMarketingStats.channel_code,
            KOLMarketingStats.stat_date,
            func.sum(KOLMarketingStats.click_count).label('daily_clicks'),
            func.sum(KOLMarketingStats.register_count).label('daily_registers'),
            func.sum(KOLMarketingStats.ftt).label('daily_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('daily_deposit')
        ).filter(*query_filter)\
         .group_by(KOLMarketingStats.channel_code, KOLMarketingStats.stat_date)\
         .order_by(KOLMarketingStats.channel_code, KOLMarketingStats.stat_date.desc()).all()

        # 计算总计
        total_stats = db.query(
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_deposit')
        ).filter(*query_filter).first()

        # 按渠道码分组数据
        channel_data = {}
        for row in channel_stats:
            channel = row.channel_code or "未知渠道"
            if channel not in channel_data:
                channel_data[channel] = []

            channel_data[channel].append({
                "stat_date": row.stat_date.isoformat() if row.stat_date else None,
                "daily_clicks": int(row.daily_clicks or 0),
                "daily_registers": int(row.daily_registers or 0),
                "daily_ftt": float(row.daily_ftt or 0),
                "daily_deposit": float(row.daily_deposit or 0)
            })

        return {
            "task_info": {
                "task_id": task_id,
                "task_name": task.task_name,
                "channel_code": channel_code
            },
            "total_stats": {
                "total_clicks": int(total_stats.total_clicks or 0),
                "total_registers": int(total_stats.total_registers or 0),
                "total_ftt": float(total_stats.total_ftt or 0),
                "total_deposit": float(total_stats.total_deposit or 0)
            },
            "channel_data": channel_data
        }

def get_task_stats_by_kol(task_id: int, kol_id: int):
    """获取KOL指定任务的统计数据（包含平台指标）"""
    import json
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table

        # 获取任务基本信息
        task = db.query(marketing_task_table).filter(marketing_task_table.c.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 验证权限：确保是该KOL的任务
        if task.kol_id != kol_id:
            raise HTTPException(status_code=403, detail="无权限查看此任务数据")

        # 获取总体统计数据
        total_stats = db.query(
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_deposit')
        ).filter(
            KOLMarketingStats.task_id == task_id,
            KOLMarketingStats.kol_id == kol_id
        ).first()

        # 获取按日期分组的统计数据
        daily_stats = db.query(
            KOLMarketingStats.stat_date,
            func.sum(KOLMarketingStats.click_count).label('daily_clicks'),
            func.sum(KOLMarketingStats.register_count).label('daily_registers'),
            func.sum(KOLMarketingStats.ftt).label('daily_ftt'),
            func.sum(KOLMarketingStats.deposit_amount).label('daily_deposit')
        ).filter(
            KOLMarketingStats.task_id == task_id,
            KOLMarketingStats.kol_id == kol_id
        ).group_by(KOLMarketingStats.stat_date)\
         .order_by(KOLMarketingStats.stat_date.desc())\
         .limit(30).all()  # 最近30天的数据

        # 获取平台指标数据 - 从 marketing_task 表查询
        task_metrics = db.query(
            marketing_task_table.c.published_links_metrics
        ).filter(
            marketing_task_table.c.id == task_id,
            marketing_task_table.c.published_links_metrics.isnot(None)
        ).first()

        # 处理平台指标数据
        platform_posts = []

        if task_metrics and task_metrics.published_links_metrics:
            try:
                metrics_data = json.loads(task_metrics.published_links_metrics)
                if "posts" in metrics_data:
                    for post in metrics_data["posts"]:
                        platform_posts.append({
                            "post_id": post.get("post_id"),
                            "post_url": post.get("post_url"),
                            "post_date": post.get("post_date"),
                            "metrics": post.get("metrics", {}),
                        })
            except json.JSONDecodeError:
                pass

        # 计算转化率
        total_clicks = int(total_stats.total_clicks or 0)
        total_registers = int(total_stats.total_registers or 0)
        register_conversion_rate = (total_registers / total_clicks * 100) if total_clicks > 0 else 0
        deposit_conversion_rate = 100 if total_registers > 0 else 0  # 假设注册用户都会入金

        # 格式化返回数据
        daily_data = []
        for row in daily_stats:
            daily_data.append({
                "stat_date": row.stat_date.isoformat() if row.stat_date else None,
                "daily_clicks": int(row.daily_clicks or 0),
                "daily_registers": int(row.daily_registers or 0),
                "daily_ftt": float(row.daily_ftt or 0),
                "daily_deposit": float(row.daily_deposit or 0)
            })

        return {
            "task_info": {
                "task_name": task.task_name,
                "task_type": task.task_type,
                "channel_code": task.channel_code
            },
            "total_stats": {
                "total_clicks": total_clicks,
                "total_registers": total_registers,
                "total_ftt": float(total_stats.total_ftt or 0),
                "total_deposit": float(total_stats.total_deposit or 0)
            },
            "daily_stats": daily_data,
            "conversion_rates": {
                "register_conversion_rate": round(register_conversion_rate, 2),
                "deposit_conversion_rate": round(deposit_conversion_rate, 2)
            },
            "platform_posts": platform_posts
        }

def get_performance_filter_options(kol_id: int):
    """获取业绩中心筛选选项"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table
        
        # 获取该KOL的所有任务名称
        task_names = db.query(marketing_task_table.c.task_name).join(
            KOLMarketingStats, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id
        ).distinct().all()
        
        # 获取该KOL的所有渠道码
        channel_codes = db.query(KOLMarketingStats.channel_code).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id,
            KOLMarketingStats.channel_code.isnot(None)
        ).distinct().all()
        
        return {
            "task_names": [{"label": name[0], "value": name[0]} for name in task_names if name[0]],
            "channel_codes": [{"label": code[0], "value": code[0]} for code in channel_codes if code[0]]
        }

def get_kol_monthly_summary_filtered(kol_id: int, start_month: str = None, end_month: str = None):
    """获取KOL按月汇总数据 - 支持日期范围筛选"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table

        query = db.query(
            KOLMarketingStats.stat_month,
            func.sum(KOLMarketingStats.deposit_amount).label('total_income'),
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(
            marketing_task_table.c.kol_id == kol_id
        )
        
        # 添加日期范围筛选
        if start_month:
            query = query.filter(KOLMarketingStats.stat_month >= start_month)
        if end_month:
            query = query.filter(KOLMarketingStats.stat_month <= end_month)
        
        monthly_data = query.group_by(KOLMarketingStats.stat_month)\
                           .order_by(KOLMarketingStats.stat_month.desc())\
                           .all()

        result = []
        for row in monthly_data:
            # 计算平均转化率 = 注册数 / 点击数
            avg_conversion_rate = (row.total_registers / row.total_clicks * 100) if row.total_clicks > 0 else 0
            result.append({
                "month": row.stat_month,
                "total_income": float(row.total_income or 0),
                "total_clicks": int(row.total_clicks or 0),
                "total_registers": int(row.total_registers or 0),
                "avg_conversion_rate": float(avg_conversion_rate),
                "total_ftt": float(row.total_ftt or 0)
            })

        return result

def get_kol_content_summary_filtered(kol_id: int, month: str = None, task_names: List[str] = None, channel_codes: List[str] = None):
    """获取KOL按任务汇总数据 - 支持多维筛选"""
    with get_db() as db:
        from services.marketing_task_service import marketing_task_table

        query = db.query(
            marketing_task_table.c.task_name,
            KOLMarketingStats.channel_code,
            func.sum(KOLMarketingStats.click_count).label('total_clicks'),
            func.sum(KOLMarketingStats.register_count).label('total_registers'),
            func.sum(KOLMarketingStats.deposit_amount).label('total_income'),
            func.sum(KOLMarketingStats.ftt).label('total_ftt'),
            func.count(KOLMarketingStats.stat_date).label('active_days')
        ).join(
            marketing_task_table, KOLMarketingStats.task_id == marketing_task_table.c.id
        ).filter(marketing_task_table.c.kol_id == kol_id)

        # 添加月份筛选
        if month:
            query = query.filter(KOLMarketingStats.stat_month == month)

        # 添加任务名称筛选
        if task_names:
            query = query.filter(marketing_task_table.c.task_name.in_(task_names))

        # 添加渠道码筛选
        if channel_codes:
            query = query.filter(KOLMarketingStats.channel_code.in_(channel_codes))

        content_data = query.group_by(
            marketing_task_table.c.task_name,
            KOLMarketingStats.channel_code
        ).order_by(desc('total_income')).all()

        result = []
        for row in content_data:
            # 计算平均转化率 = 注册数 / 点击数
            avg_conversion_rate = (row.total_registers / row.total_clicks * 100) if row.total_clicks > 0 else 0
            result.append({
                "campaign_name": row.task_name or "未知任务",
                "channel_code": row.channel_code,
                "total_clicks": int(row.total_clicks or 0),
                "total_registers": int(row.total_registers or 0),
                "total_income": float(row.total_income or 0),
                "total_ftt": float(row.total_ftt or 0),
                "avg_conversion_rate": float(avg_conversion_rate),
                "active_days": int(row.active_days or 0)
            })

        return result