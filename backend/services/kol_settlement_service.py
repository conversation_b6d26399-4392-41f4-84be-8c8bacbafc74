from sqlalchemy import Table, select, func, case
from sqlalchemy.engine import Result
from models import metadata, engine
from sqlalchemy.orm import Session
from models.db import get_db
from models.settlement import KolSettlement
from typing import List, Optional

# 使用正确的表名
settlement_detail = Table('settlement_detail', metadata, autoload_with=engine)

class KolSettlementService:
    @staticmethod
    def get_settlements_by_user_id(user_id: int, offset: int = 0, limit: int = 100) -> List[KolSettlement]:
        """
        根据广告主ID获取结算信息列表
        """
        with get_db() as db:
            return (
                db.query(KolSettlement)
                .filter(KolSettlement.bit_id == user_id)
                .offset(offset).limit(limit)
                .all()
            )

    @staticmethod
    def get_settlements_by_kol_id(kol_id: int, offset: int = 0, limit: int = 100, task_id: Optional[int] = None) -> List[KolSettlement]:
        """
        根据KOL ID获取结算信息列表，支持按任务筛选
        """
        with get_db() as db:
            query = db.query(KolSettlement).filter(KolSettlement.kol_id == kol_id)

            # 如果指定了 task_id，添加筛选条件
            if task_id is not None:
                query = query.filter(KolSettlement.task_id == task_id)

            return query.offset(offset).limit(limit).all()

    @staticmethod
    def get_kol_settlements_with_task_info(
        kol_id: int, 
        offset: int = 0, 
        limit: int = 100,
        settlement_month: Optional[str] = None,
        status: Optional[str] = None,
        payment_method: Optional[str] = None
    ) -> dict:
        """
        获取KOL结算记录（包含任务信息），支持筛选和分页
        
        Args:
            kol_id: KOL用户ID
            offset: 偏移量
            limit: 限制数量
            settlement_month: 结算月份筛选 (YYYY-MM)
            status: 状态筛选 (pending/paid/failed)
            payment_method: 支付方式筛选
            
        Returns:
            {
                "data": List[dict],  # 结算记录列表
                "total": int,        # 总记录数
                "summary": dict      # 汇总统计
            }
        """
        from services.marketing_task_service import marketing_task_table

        with get_db() as db:
            # 构建基础查询 - 关联 marketing_task 表
            base_query = db.query(
                KolSettlement.id,
                KolSettlement.kol_id,
                KolSettlement.bit_id,
                KolSettlement.task_id,
                marketing_task_table.c.task_name,  # 关联查询任务名称
                KolSettlement.settlement_month,
                KolSettlement.settlement_date,
                KolSettlement.marketing_count,
                KolSettlement.base_reward,
                KolSettlement.base_total,
                KolSettlement.performance_value,
                KolSettlement.performance_rate,
                KolSettlement.performance_total,
                KolSettlement.total_fee,
                KolSettlement.status,
                KolSettlement.payment_method,
                KolSettlement.wallet_address,
                KolSettlement.transaction_hash,
                KolSettlement.payment_network,
                KolSettlement.paid_time,
                KolSettlement.payment_note,
                KolSettlement.create_time,
                KolSettlement.update_time
            ).join(
                marketing_task_table, KolSettlement.task_id == marketing_task_table.c.id
            ).filter(
                KolSettlement.kol_id == kol_id
            )
            
            # 添加筛选条件
            if settlement_month:
                base_query = base_query.filter(KolSettlement.settlement_month == settlement_month)
            if status:
                base_query = base_query.filter(KolSettlement.status == status)
            if payment_method:
                base_query = base_query.filter(KolSettlement.payment_method == payment_method)
            
            # 获取总数
            total_count = base_query.count()
            
            # 获取分页数据
            settlements = base_query.order_by(
                KolSettlement.create_time.desc()
            ).offset(offset).limit(limit).all()
            
            # 格式化数据 - 统一字段名称
            formatted_data = []
            for row in settlements:
                formatted_data.append({
                    "id": row.id,
                    "kol_id": row.kol_id,
                    "bit_id": row.bit_id,
                    "task_id": row.task_id,
                    "task_name": row.task_name,  # 前端需要的字段
                    "settlement_month": row.settlement_month,
                    "settlement_date": row.settlement_date.isoformat() if row.settlement_date else None,
                    "marketing_count": row.marketing_count,
                    "base_reward": float(row.base_reward),
                    "base_fee": float(row.base_total),  # 字段名映射
                    "performance_value": float(row.performance_value),
                    "performance_rate": float(row.performance_rate),
                    "performance_fee": float(row.performance_total),  # 字段名映射
                    "total_fee": float(row.total_fee),
                    "status": row.status,
                    "payment_method": row.payment_method,
                    "wallet_address": row.wallet_address,
                    "tx_hash": row.transaction_hash,  # 字段名映射
                    "payment_network": row.payment_network,
                    "paid_time": row.paid_time.isoformat() if row.paid_time else None,
                    "payment_note": row.payment_note,
                    "created_at": row.create_time.isoformat(),  # 字段名映射
                    "updated_at": row.update_time.isoformat()
                })
            
            # 获取汇总统计
            summary_stats = db.query(
                func.sum(KolSettlement.total_fee).label('total_amount'),
                func.sum(case((KolSettlement.status == 'paid', KolSettlement.total_fee), else_=0)).label('paid_amount'),
                func.sum(case((KolSettlement.status == 'pending', KolSettlement.total_fee), else_=0)).label('pending_amount'),
                func.sum(case((KolSettlement.status == 'failed', KolSettlement.total_fee), else_=0)).label('failed_amount'),
                func.count(KolSettlement.id).label('total_count'),
                func.sum(case((KolSettlement.status == 'paid', 1), else_=0)).label('paid_count'),
                func.sum(case((KolSettlement.status == 'pending', 1), else_=0)).label('pending_count'),
                func.sum(case((KolSettlement.status == 'failed', 1), else_=0)).label('failed_count')
            ).filter(KolSettlement.kol_id == kol_id).first()
            
            summary = {
                "total_amount": float(summary_stats.total_amount or 0),
                "paid_amount": float(summary_stats.paid_amount or 0),
                "pending_amount": float(summary_stats.pending_amount or 0),
                "failed_amount": float(summary_stats.failed_amount or 0),
                "total_count": int(summary_stats.total_count or 0),
                "paid_count": int(summary_stats.paid_count or 0),
                "pending_count": int(summary_stats.pending_count or 0),
                "failed_count": int(summary_stats.failed_count or 0)
            }
            
            return {
                "data": formatted_data,
                "total": total_count,
                "summary": summary
            }

    @staticmethod
    def get_settlement_detail(settlement_id: int) -> Optional[dict]:
        """
        获取单个结算记录的详细信息
        """
        from models.MarketingTask import MarketingTask
        
        with get_db() as db:
            result = db.query(
                KolSettlement,
                MarketingTask.task_name
            ).join(
                MarketingTask, KolSettlement.task_id == MarketingTask.id
            ).filter(
                KolSettlement.id == settlement_id
            ).first()
            
            if not result:
                return None
                
            settlement, task_name = result
            
            return {
                "id": settlement.id,
                "kol_id": settlement.kol_id,
                "bit_id": settlement.bit_id,
                "task_id": settlement.task_id,
                "task_name": task_name,
                "settlement_month": settlement.settlement_month,
                "settlement_date": settlement.settlement_date.isoformat() if settlement.settlement_date else None,
                "marketing_count": settlement.marketing_count,
                "base_reward": float(settlement.base_reward),
                "base_fee": float(settlement.base_total),
                "performance_value": float(settlement.performance_value),
                "performance_rate": float(settlement.performance_rate),
                "performance_fee": float(settlement.performance_total),
                "total_fee": float(settlement.total_fee),
                "status": settlement.status,
                "payment_method": settlement.payment_method,
                "wallet_address": settlement.wallet_address,
                "tx_hash": settlement.transaction_hash,
                "payment_network": settlement.payment_network,
                "paid_time": settlement.paid_time.isoformat() if settlement.paid_time else None,
                "payment_note": settlement.payment_note,
                "created_at": settlement.create_time.isoformat(),
                "updated_at": settlement.update_time.isoformat()
            }

    @staticmethod
    def get_settlement_by_id(settlement_id: int) -> KolSettlement:
        """
        根据结算ID获取单条结算信息
        """
        with get_db() as db:
            return (
                db.query(KolSettlement)
                .filter(KolSettlement.id == settlement_id)
                .first()
            )

    @staticmethod
    def get_settlement_summary(kol_id: int):
        """获取KOL结算汇总统计"""
        from sqlalchemy import func
        
        with engine.connect() as conn:
            # 总结算统计
            total_stats = conn.execute(
                select(
                    func.count(settlement_detail.c.id).label('total_settlements'),
                    func.sum(settlement_detail.c.total_fee).label('total_amount'),
                    func.sum(settlement_detail.c.base_total).label('total_base_fee'),
                    func.sum(settlement_detail.c.performance_total).label('total_commission'),
                    func.avg(settlement_detail.c.total_fee).label('avg_amount')
                ).where(settlement_detail.c.kol_id == kol_id)
            ).first()

            # 按状态统计
            status_stats = conn.execute(
                select(
                    settlement_detail.c.status,
                    func.count(settlement_detail.c.id).label('count'),
                    func.sum(settlement_detail.c.total_fee).label('amount')
                ).where(settlement_detail.c.kol_id == kol_id)
                .group_by(settlement_detail.c.status)
            ).all()

            # 格式化状态统计
            status_summary = {}
            for row in status_stats:
                status_summary[row.status] = {
                    'count': int(row.count or 0),
                    'amount': float(row.amount or 0)
                }

            return {
                'total_settlements': int(total_stats.total_settlements or 0),
                'total_amount': float(total_stats.total_amount or 0),
                'total_base_fee': float(total_stats.total_base_fee or 0),
                'total_commission': float(total_stats.total_commission or 0),
                'avg_amount': float(total_stats.avg_amount or 0),
                'status_summary': status_summary
            }