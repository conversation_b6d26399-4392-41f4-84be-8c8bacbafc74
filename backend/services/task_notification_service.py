from typing import Dict, List, Optional
from datetime import datetime
from models.Message import Message
from services.message_service import add_message
from utils.logger import logger

class TaskNotificationService:
    """任务通知服务"""
    
    # 通知类型
    NOTIFICATION_TYPE_TASK = "task"
    NOTIFICATION_TYPE_REVIEW = "review"
    NOTIFICATION_TYPE_COMPLETION = "completion"
    NOTIFICATION_TYPE_CANCELLATION = "cancellation"
    
    @classmethod
    def notify_status_change(cls, task: Dict, old_status: str, new_status: str, 
                           operator_id: int, feedback: Optional[str] = None):
        """通知任务状态变更"""
        try:
            # 获取通知内容
            notification_content = cls._generate_notification_content(
                task, old_status, new_status, feedback
            )
            
            # 确定通知类型
            notification_type = cls._get_notification_type(old_status, new_status)
            
            # 确定通知用户
            notify_users = cls._get_notify_users(task, old_status, new_status, operator_id)
            
            # 发送通知
            for user_id in notify_users:
                cls._send_notification(user_id, notification_content, notification_type, task)
                
            logger.info(f"任务状态变更通知已发送: {task['id']} {old_status} -> {new_status}")
            
        except Exception as e:
            logger.error(f"发送任务状态变更通知失败: {str(e)}")
    
    @classmethod
    def _generate_notification_content(cls, task: Dict, old_status: str, new_status: str, 
                                     feedback: Optional[str] = None) -> str:
        """生成通知内容"""
        task_name = task['task_name']
        old_status_name = cls._get_status_display_name(old_status)
        new_status_name = cls._get_status_display_name(new_status)
        
        # 基础通知内容
        base_content = f"任务【{task_name}】状态已从【{old_status_name}】变更为【{new_status_name}】"
        
        # 根据状态变更类型添加详细信息
        if new_status == "assigned":
            content = f"🎉 {base_content}\n\nKOL已接受您的任务，现在可以开始执行了！"
        elif new_status == "unapproved":
            content = f"📝 {base_content}\n\nKOL已提交内容，请及时审核。"
        elif new_status == "approved":
            content = f"✅ {base_content}\n\n内容审核通过！KOL可以发布内容了。"
        elif new_status == "rejected":
            feedback_text = f"\n\n审核意见：{feedback}" if feedback else ""
            content = f"❌ {base_content}{feedback_text}\n\n请根据审核意见修改内容后重新提交。"
        elif new_status == "completed":
            content = f"🎊 {base_content}\n\n恭喜！任务已完成，请查看最终效果。"
        elif new_status == "cancelled":
            content = f"🚫 {base_content}\n\n任务已取消，如有疑问请联系相关人员。"
        else:
            content = base_content
        
        return content
    
    @classmethod
    def _get_notification_type(cls, old_status: str, new_status: str) -> str:
        """获取通知类型"""
        if new_status == "approved" or new_status == "rejected":
            return cls.NOTIFICATION_TYPE_REVIEW
        elif new_status == "completed":
            return cls.NOTIFICATION_TYPE_COMPLETION
        elif new_status == "cancelled":
            return cls.NOTIFICATION_TYPE_CANCELLATION
        else:
            return cls.NOTIFICATION_TYPE_TASK
    
    @classmethod
    def _get_notify_users(cls, task: Dict, old_status: str, new_status: str, operator_id: int) -> List[int]:
        """确定需要通知的用户"""
        notify_users = []
        creator_id = task.get('creator')
        kol_id = task.get('kol_id')
        
        logger.info(f"确定通知用户: 任务ID={task.get('id')}, 创建者={creator_id}, KOL_ID={kol_id}, 操作者={operator_id}")
        logger.info(f"状态变更: {old_status} -> {new_status}")
        
        # 根据状态变更类型确定通知对象
        if new_status == "assigned":
            # KOL接受任务，通知Bit
            if creator_id and creator_id != operator_id:
                notify_users.append(creator_id)
                logger.info(f"KOL接受任务，通知Bit用户: {creator_id}")
        elif new_status == "unapproved":
            # KOL提交内容，通知Bit
            if creator_id and creator_id != operator_id:
                notify_users.append(creator_id)
                logger.info(f"KOL提交内容，通知Bit用户: {creator_id}")
        elif new_status == "approved" or new_status == "rejected":
            # Bit审核结果，通知KOL
            if kol_id and kol_id != operator_id:
                notify_users.append(kol_id)
                logger.info(f"Bit审核结果，通知KOL用户: {kol_id}")
        elif new_status == "completed":
            # 任务完成，通知双方
            if creator_id and creator_id != operator_id:
                notify_users.append(creator_id)
            if kol_id and kol_id != operator_id:
                notify_users.append(kol_id)
            logger.info(f"任务完成，通知用户: 创建者={creator_id}, KOL={kol_id}")
        elif new_status == "cancelled":
            # 任务取消，通知双方
            if creator_id and creator_id != operator_id:
                notify_users.append(creator_id)
            if kol_id and kol_id != operator_id:
                notify_users.append(kol_id)
            logger.info(f"任务取消，通知用户: 创建者={creator_id}, KOL={kol_id}")
        else:
            # 其他状态变更，通知双方
            if creator_id and creator_id != operator_id:
                notify_users.append(creator_id)
            if kol_id and kol_id != operator_id:
                notify_users.append(kol_id)
            logger.info(f"其他状态变更，通知用户: 创建者={creator_id}, KOL={kol_id}")
        
        # 去重并记录
        unique_users = list(set(notify_users))
        logger.info(f"最终通知用户列表: {unique_users}")
        return unique_users
    
    @classmethod
    def _send_notification(cls, user_id: int, content: str, message_type: str, task: Dict):
        """发送通知消息"""
        try:
            logger.info(f"准备发送通知给用户 {user_id}: {content[:50]}...")
            
            from services.message_service import MessageService, MessageCreate
            from models.db import get_db
            
            with get_db() as db:
                service = MessageService(db)
                message_data = MessageCreate(
                    user_id=user_id,
                    content=content
                )
                # 创建消息并设置类型
                message = service.create_message(message_data)
                # 更新消息类型
                db.query(Message).filter_by(id=message.id).update({
                    "message_type": message_type
                })
                db.commit()
                
                logger.info(f"✅ 通知已成功发送给用户 {user_id}: {content[:50]}...")
                
        except Exception as e:
            logger.error(f"❌ 发送通知失败: {str(e)}")
            raise
    
    @classmethod
    def _get_status_display_name(cls, status: str) -> str:
        """获取状态的中文显示名称"""
        status_names = {
            "draft": "草稿",
            "published": "已发布",
            "assigned": "已分配",
            "unapproved": "待审核",
            "approved": "审核通过",
            "rejected": "审核拒绝待修改",
            "completed": "已完成",
            "cancelled": "已取消"
        }
        return status_names.get(status, status)
    
    @classmethod
    def notify_task_reminder(cls, task: Dict, reminder_type: str):
        """发送任务提醒"""
        task_name = task['task_name']
        
        if reminder_type == "deadline_approaching":
            content = f"⏰ 提醒：任务【{task_name}】即将到期，请及时处理！"
        elif reminder_type == "overdue":
            content = f"🚨 警告：任务【{task_name}】已逾期，请立即处理！"
        elif reminder_type == "review_pending":
            content = f"📋 待办：任务【{task_name}】等待审核，请及时处理！"
        else:
            content = f"📢 提醒：任务【{task_name}】需要您的关注！"
        
        # 根据任务状态确定通知对象
        notify_users = []
        if task['task_status'] == "unapproved":
            # 待审核状态，通知Bit
            notify_users.append(task['creator'])
        elif task['task_status'] == "assigned":
            # 已分配状态，通知KOL
            notify_users.append(task['kol_id'])
        
        for user_id in notify_users:
            cls._send_notification(user_id, content, "reminder", task)
    
    @classmethod
    def notify_bulk_status_change(cls, tasks: List[Dict], new_status: str, operator_id: int):
        """批量通知状态变更"""
        for task in tasks:
            old_status = task.get('task_status', 'unknown')
            cls.notify_status_change(task, old_status, new_status, operator_id) 