from typing import Optional, Dict, List
from datetime import datetime
from sqlalchemy import Table, select, update
from sqlalchemy.engine import Result
from models import metadata, engine
from fastapi import HTTPException
from utils.logger import logger
from services.task_notification_service import TaskNotificationService

marketing_task_table = Table('marketing_task', metadata, autoload_with=engine)

class TaskStatusService:
    """任务状态管理服务"""
    
    # 任务状态定义
    STATUS_DRAFT = "draft"                    # 草稿
    STATUS_PUBLISHED = "published"            # 已发布
    STATUS_ASSIGNED = "assigned"              # 已分配
    STATUS_UNAPPROVED = "unapproved"          # 待审核
    STATUS_APPROVED = "approved"              # 审核通过
    STATUS_REJECTED = "rejected"              # 审核拒绝待修改
    STATUS_RUNNING = "running"                # 运行中
    STATUS_COMPLETED = "completed"            # 已完成
    STATUS_CANCELLED = "cancelled"            # 已取消
    
    # 状态流转规则 - 根据业务流程定义
    STATUS_FLOW = {
        STATUS_DRAFT: [STATUS_PUBLISHED],                    # 草稿 -> 已发布
        STATUS_PUBLISHED: [STATUS_ASSIGNED, STATUS_CANCELLED],   # 已发布 -> 已分配/已取消
        STATUS_ASSIGNED: [STATUS_UNAPPROVED, STATUS_APPROVED, STATUS_REJECTED, STATUS_CANCELLED],  # 已分配 -> 待审核/审核通过/审核拒绝待修改/已取消
        STATUS_UNAPPROVED: [STATUS_APPROVED, STATUS_REJECTED, STATUS_CANCELLED],  # 待审核 -> 审核通过/审核拒绝待修改/已取消
        STATUS_APPROVED: [STATUS_RUNNING, STATUS_CANCELLED],     # 审核通过 -> 运行中/已取消
        STATUS_REJECTED: [STATUS_CANCELLED],  # 审核拒绝待修改 -> 已取消
        STATUS_RUNNING: [STATUS_COMPLETED, STATUS_CANCELLED],    # 运行中 -> 已完成/已取消
        STATUS_COMPLETED: [],                          # 已完成 - 终态
        STATUS_CANCELLED: []                           # 已取消 - 终态
    }
    
    @classmethod
    def get_available_statuses(cls, current_status: str) -> List[str]:
        """获取当前状态可以流转到的状态列表"""
        return cls.STATUS_FLOW.get(current_status, [])
    
    @classmethod
    def can_transition_to(cls, current_status: str, target_status: str) -> bool:
        """检查状态流转是否合法"""
        available_statuses = cls.get_available_statuses(current_status)
        return target_status in available_statuses
    
    @classmethod
    def get_task_by_id(cls, task_id: int) -> Optional[Dict]:
        """根据ID获取任务"""
        with engine.connect() as conn:
            stmt = select(marketing_task_table).where(marketing_task_table.c.id == task_id)
            result: Result = conn.execute(stmt)
            row = result.first()
            return dict(row._mapping) if row else None
    
    @classmethod
    def update_task_status(cls, task_id: int, new_status: str, operator_id: int, 
                          feedback: Optional[str] = None, kol_id: Optional[int] = None) -> Dict:
        """更新任务状态"""
        # 获取当前任务
        task = cls.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        current_status = task['task_status']
        
        # 检查状态流转是否合法
        if not cls.can_transition_to(current_status, new_status):
            raise HTTPException(
                status_code=400, 
                detail=f"非法状态流转：{current_status} -> {new_status}"
            )
        
        # 更新任务状态
        with engine.connect() as conn:
            update_data = {
                'task_status': new_status,
                'update_time': datetime.now()
            }
            
            # 根据新状态设置相应的字段
            if new_status == cls.STATUS_ASSIGNED:
                update_data['assigned_time'] = datetime.now()
                # 如果提供了KOL ID，则设置KOL ID
                if kol_id is not None:
                    update_data['kol_id'] = kol_id
            elif new_status == cls.STATUS_UNAPPROVED:
                update_data['draft_submit_time'] = datetime.now()
            elif new_status == cls.STATUS_APPROVED or new_status == cls.STATUS_REJECTED:
                update_data['draft_reviewed_at'] = datetime.now()
                if feedback:
                    update_data['review_feedback'] = feedback
            
            stmt = (
                update(marketing_task_table)
                .where(marketing_task_table.c.id == task_id)
                .values(**update_data)
            )
            conn.execute(stmt)
            conn.commit()
        
        # 发送通知消息
        TaskNotificationService.notify_status_change(task, current_status, new_status, operator_id, feedback)
        
        logger.info(f"任务状态更新：{task_id} {current_status} -> {new_status}")
        
        return {
            "success": True,
            "task_id": task_id,
            "old_status": current_status,
            "new_status": new_status,
            "message": cls._get_status_change_message(current_status, new_status)
        }
    

    
    @classmethod
    def _get_status_display_name(cls, status: str) -> str:
        """获取状态的中文显示名称"""
        status_names = {
            cls.STATUS_DRAFT: "草稿",
            cls.STATUS_PUBLISHED: "已发布",
            cls.STATUS_ASSIGNED: "已分配",
            cls.STATUS_UNAPPROVED: "待审核",
            cls.STATUS_APPROVED: "审核通过",
            cls.STATUS_REJECTED: "审核拒绝待修改",
            cls.STATUS_RUNNING: "运行中",
            cls.STATUS_COMPLETED: "已完成",
            cls.STATUS_CANCELLED: "已取消"
        }
        return status_names.get(status, status)
    
    @classmethod
    def _get_status_change_message(cls, old_status: str, new_status: str) -> str:
        """获取状态变更的业务说明"""
        messages = {
            (cls.STATUS_DRAFT, cls.STATUS_PUBLISHED): "任务已发布到需求池",
            (cls.STATUS_PUBLISHED, cls.STATUS_ASSIGNED): "KOL已接受任务",
            (cls.STATUS_ASSIGNED, cls.STATUS_UNAPPROVED): "KOL已提交内容，等待审核",
            (cls.STATUS_UNAPPROVED, cls.STATUS_APPROVED): "内容审核通过",
            (cls.STATUS_UNAPPROVED, cls.STATUS_REJECTED): "内容审核不通过，需要修改",
            (cls.STATUS_REJECTED, cls.STATUS_UNAPPROVED): "KOL已修改内容，重新提交审核",
            (cls.STATUS_APPROVED, cls.STATUS_COMPLETED): "任务已完成",
            (cls.STATUS_PUBLISHED, cls.STATUS_CANCELLED): "任务已取消",
            (cls.STATUS_ASSIGNED, cls.STATUS_CANCELLED): "任务已取消",
            (cls.STATUS_UNAPPROVED, cls.STATUS_CANCELLED): "任务已取消",
            (cls.STATUS_APPROVED, cls.STATUS_CANCELLED): "任务已取消"
        }
        return messages.get((old_status, new_status), f"状态从{cls._get_status_display_name(old_status)}变更为{cls._get_status_display_name(new_status)}")

# 业务流程说明
"""
KOL任务状态流程：

1. 创建任务
   - Bit创建任务：draft -> published (直接指定KOL)
   - Kol申请任务：published -> assigned (需求池申请)

2. 任务分配
   - KOL申请任务，Bit同意：published -> assigned
   - Bit指定KOL，KOL接受：published -> assigned

3. KOL提交内容
   - KOL提交资料给Bit审核：assigned -> unapproved
   - 或者Bit直接审核KOL提交的资料：assigned -> approved/rejected

4. Bit审核
   - Bit审核通过：unapproved -> approved 或 assigned -> approved
   - Bit审核不通过：unapproved -> rejected 或 assigned -> rejected

5. 任务完成
   - Bit审核通过后，任务进入运行中：approved -> running
   - 运行中状态下，可以完成任务：running -> completed
   - 期间Bit可以取消任务：任何状态 -> cancelled

状态流转规则：
- draft -> published (创建任务)
- published -> assigned/cancelled (KOL接受或取消)
- assigned -> unapproved/approved/rejected/cancelled (提交内容、直接审核或取消)
- unapproved -> approved/rejected/cancelled (审核结果或取消)
- approved -> running/cancelled (审核通过后进入运行中或取消)
- rejected -> cancelled (审核拒绝后只能取消)
- completed/cancelled -> 终态
""" 