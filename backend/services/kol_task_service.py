from sqlalchemy import Table, select, update, insert
from sqlalchemy.engine import Result
from models import metadata, engine
from models.MarketingTask import Material
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from fastapi import HTTPException
import logging
import json

logger = logging.getLogger(__name__)

marketing_task_table = Table('marketing_task', metadata, autoload_with=engine)

class TaskAcceptRequest(BaseModel):
    task_id: int
    kol_id: int
    accept_reason: Optional[str] = None

class ContentSubmitRequest(BaseModel):
    task_id: int
    kol_id: int
    content_url: str
    content_description: str
    promotion_links: Optional[str] = None

class DraftSubmitRequest(BaseModel):
    draft_content: str
    creation_notes: Optional[str] = None
    materials: Optional[List[Material]] = None

class PublishLinksRequest(BaseModel):
    platform: str
    links: List[str]
    publish_time: Optional[datetime] = None
    notes: Optional[str] = None

def get_kol_task(kol_id: int = None, status: str = None):
    """读取 marketing_task 表的内容，支持按KOL和状态筛选"""
    logger.info(f"获取KOL任务: kol_id={kol_id}, status={status}")
    
    with engine.connect() as conn:
        stmt = select(marketing_task_table)

        # 如果指定了kol_id，只返回该KOL的任务
        if kol_id:
            stmt = stmt.where(marketing_task_table.c.kol_id == kol_id)
            logger.info(f"筛选KOL ID: {kol_id}")

        # 如果指定了状态，按状态筛选
        if status:
            stmt = stmt.where(marketing_task_table.c.task_status == status)
            logger.info(f"筛选状态: {status}")

        result: Result = conn.execute(stmt)
        rows = [dict(row._mapping) for row in result]
        
        logger.info(f"查询结果: 找到 {len(rows)} 个任务")
        for row in rows:
            logger.info(f"  任务ID: {row['id']}, 名称: {row['task_name']}, 状态: {row['task_status']}, KOL_ID: {row['kol_id']}")
    
    return rows

def accept_task(request: TaskAcceptRequest):
    """KOL接收任务"""
    with engine.connect() as conn:
        # 检查任务是否存在且状态为"published"(已发布)
        task_check = conn.execute(
            select(marketing_task_table).where(marketing_task_table.c.id == request.task_id)
        ).first()

        if not task_check:
            raise HTTPException(status_code=404, detail="任务不存在")

        if task_check.task_status != "published":
            raise HTTPException(status_code=400, detail="任务状态不允许接收")

        # 更新任务状态为"assigned"(已分配)
        stmt = update(marketing_task_table).where(
            marketing_task_table.c.id == request.task_id
        ).values(
            task_status="assigned",
            kol_id=request.kol_id,
            assigned_time=datetime.now(),
            update_time=datetime.now()
        )

        conn.execute(stmt)
        conn.commit()

        return {"success": True, "message": "任务接收成功"}

def submit_content(request: ContentSubmitRequest):
    """KOL提交任务内容"""
    with engine.connect() as conn:
        # 检查任务是否存在且状态为"assigned"(已分配)
        task_check = conn.execute(
            select(marketing_task_table).where(
                marketing_task_table.c.id == request.task_id,
                marketing_task_table.c.kol_id == request.kol_id
            )
        ).first()

        if not task_check:
            raise HTTPException(status_code=404, detail="任务不存在或无权限")

        if task_check.task_status != "assigned":
            raise HTTPException(status_code=400, detail="任务状态不允许提交内容")

        # 更新任务状态为"unapproved"(待审核)
        stmt = update(marketing_task_table).where(
            marketing_task_table.c.id == request.task_id
        ).values(
            task_status="unapproved",
            draft_content=request.content_description,
            published_links=request.promotion_links,
            draft_submit_time=datetime.now(),
            update_time=datetime.now()
        )

        conn.execute(stmt)
        conn.commit()

        return {"success": True, "message": "内容提交成功"}

def get_task_by_id(task_id: int):
    """根据ID获取任务详情"""
    with engine.connect() as conn:
        stmt = select(marketing_task_table).where(marketing_task_table.c.id == task_id)
        result = conn.execute(stmt).first()

        if not result:
            raise HTTPException(status_code=404, detail="任务不存在")

        return dict(result._mapping)

def submit_draft(task_id: int, kol_id: int, request: DraftSubmitRequest):
    """KOL提交任务草稿"""
    with engine.connect() as conn:
        # 先检查任务是否存在
        task_exists = conn.execute(
            select(marketing_task_table).where(marketing_task_table.c.id == task_id)
        ).first()

        if not task_exists:
            logger.error(f"任务不存在: task_id={task_id}")
            raise HTTPException(status_code=404, detail=f"任务{task_id}不存在")

        # 记录任务信息用于调试
        task_dict = dict(task_exists._mapping)
        logger.info(f"任务信息: task_id={task_id}, kol_id={task_dict.get('kol_id')}, 当前用户={kol_id}")

        # 检查任务是否属于该KOL
        if task_dict.get('kol_id') != kol_id:
            logger.error(f"权限检查失败: 任务kol_id={task_dict.get('kol_id')}, 当前用户={kol_id}")
            raise HTTPException(status_code=403, detail=f"无权限操作任务{task_id}，该任务不属于当前用户")

        task_check = task_exists

        # 检查任务状态是否允许提交草稿
        if task_dict['task_status'] not in ['assigned', 'rejected']:
            logger.error(f"任务状态不允许提交草稿: task_id={task_id}, 当前状态={task_dict['task_status']}")
            raise HTTPException(status_code=400, detail=f"当前任务状态'{task_dict['task_status']}'不允许提交草稿，只有'assigned'或'rejected'状态才能提交")

        # 构建草稿内容数据
        materials_data = []
        if request.materials:
            for material in request.materials:
                material_dict = material.dict()
                # 处理 datetime 对象，转换为 ISO 格式字符串
                if material_dict.get('submit_time') and hasattr(material_dict['submit_time'], 'isoformat'):
                    material_dict['submit_time'] = material_dict['submit_time'].isoformat()
                materials_data.append(material_dict)

        draft_data = {
            "content": request.draft_content,
            "creation_notes": request.creation_notes,
            "materials": materials_data
        }

        # 更新任务草稿内容和状态
        stmt = update(marketing_task_table).where(
            marketing_task_table.c.id == task_id
        ).values(
            task_status="unapproved",
            draft_content=json.dumps(draft_data, ensure_ascii=False),
            draft_submit_time=datetime.now(),
            update_time=datetime.now()
        )

        conn.execute(stmt)
        conn.commit()

        return {"success": True, "message": "草稿提交成功", "task_status": "unapproved"}

def submit_publish_links(task_id: int, kol_id: int, request: PublishLinksRequest):
    """KOL提交正式发布链接"""
    import json

    with engine.connect() as conn:
        # 检查任务是否存在且属于该KOL
        task_check = conn.execute(
            select(marketing_task_table).where(
                marketing_task_table.c.id == task_id,
                marketing_task_table.c.kol_id == kol_id
            )
        ).first()

        if not task_check:
            raise HTTPException(status_code=404, detail="任务不存在或无权限")

        task_dict = dict(task_check._mapping)
        # 检查任务状态是否为已审核通过
        if task_dict['task_status'] != 'approved':
            raise HTTPException(status_code=400, detail="只有审核通过的任务才能提交发布链接")

        # 构建发布链接数据
        publish_data = {
            "platform": request.platform,
            "links": request.links,
            "publish_time": request.publish_time.isoformat() if request.publish_time else None,
            "notes": request.notes or "",
            "submit_time": datetime.now().isoformat()
        }

        # 更新任务发布链接和状态
        stmt = update(marketing_task_table).where(
            marketing_task_table.c.id == task_id
        ).values(
            task_status="running",  # 改为 running 状态，表示任务开始正式运行
            published_links=json.dumps(publish_data, ensure_ascii=False),
            update_time=datetime.now()
        )

        conn.execute(stmt)
        conn.commit()

        return {"success": True, "message": "发布链接提交成功，任务开始运行", "task_status": "running"}

if __name__ == "__main__":
    # 测试读取
    from pprint import pprint
    pprint(get_kol_task())